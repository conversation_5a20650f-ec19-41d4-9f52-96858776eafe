package com.shuimu.course.data.remote.api

import com.shuimu.course.config.ApiConfig
import retrofit2.Response
import retrofit2.http.*

/**
 * 用户个人数据API接口
 * 包括观看进度、缓存状态、收藏等个人数据
 * 遵循RESTful设计原则
 */
interface UserDataApi {

    // ========== 用户观看进度 ==========
    
    /**
     * 获取用户所有观看进度
     */
    @GET(ApiConfig.Endpoints.USER_PROGRESS)
    suspend fun getUserAllProgress(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserProgressListResponseDto>

    /**
     * 获取用户特定视频的观看进度
     */
    @GET(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun getUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserProgressResponseDto>

    /**
     * 更新用户视频观看进度
     */
    @PUT(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun updateUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body progressRequest: UserProgressUpdateRequestDto
    ): Response<UserProgressResponseDto>

    /**
     * 删除用户视频观看进度
     */
    @DELETE(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun deleteUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<DeleteResponseDto>

    // ========== 用户缓存管理 ==========
    
    /**
     * 获取用户缓存列表
     */
    @GET(ApiConfig.Endpoints.USER_CACHE)
    suspend fun getUserCacheList(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserCacheListResponseDto>

    /**
     * 更新用户视频缓存状态
     */
    @PUT(ApiConfig.Endpoints.USER_CACHE_VIDEO)
    suspend fun updateUserVideoCache(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body cacheRequest: UserCacheUpdateRequestDto
    ): Response<UserCacheResponseDto>

    /**
     * 删除用户视频缓存记录
     */
    @DELETE(ApiConfig.Endpoints.USER_CACHE_VIDEO)
    suspend fun deleteUserVideoCache(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<DeleteResponseDto>

    // ========== 用户收藏管理 ==========
    
    /**
     * 获取用户收藏列表
     */
    @GET(ApiConfig.Endpoints.USER_FAVORITES)
    suspend fun getUserFavorites(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserFavoritesResponseDto>

    /**
     * 更新用户收藏
     */
    @PUT(ApiConfig.Endpoints.USER_FAVORITES)
    suspend fun updateUserFavorites(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body favoritesRequest: UserFavoritesUpdateRequestDto
    ): Response<UserFavoritesResponseDto>

    // ========== 废弃的端点（向后兼容） ==========
    
    /**
     * @deprecated 使用 updateUserVideoProgress 替代
     */
    @Deprecated("使用 updateUserVideoProgress 替代")
    @PUT("/api/videos/{video_id}/progress")
    suspend fun updateVideoProgressDeprecated(
        @Path("video_id") videoId: String,
        @Body progressRequest: VideoProgressUpdateRequestDto
    ): Response<VideoProgressResponseDto>

    /**
     * @deprecated 使用 updateUserVideoCache 替代
     */
    @Deprecated("使用 updateUserVideoCache 替代")
    @PUT("/api/videos/{video_id}/cache-status")
    suspend fun updateVideoCacheStatusDeprecated(
        @Path("video_id") videoId: String,
        @Body cacheRequest: VideoCacheUpdateRequestDto
    ): Response<VideoCacheResponseDto>
}

// ========== 数据传输对象 ==========

/**
 * 用户进度更新请求
 */
data class UserProgressUpdateRequestDto(
    val position: Int? = null,
    val progress: Float? = null,
    val watchCount: Int? = null,
    val isCompleted: Boolean? = null,
    val lastWatchedAt: String? = null
)

/**
 * 用户缓存更新请求
 */
data class UserCacheUpdateRequestDto(
    val isCached: Boolean,
    val localPath: String? = null,
    val fileSize: Long? = null,
    val cachedAt: String? = null
)

/**
 * 用户收藏更新请求
 */
data class UserFavoritesUpdateRequestDto(
    val favoriteVideos: List<String>? = null,
    val favoriteCategories: List<String>? = null,
    val favoriteSeries: List<String>? = null
)

/**
 * 用户进度响应
 */
data class UserProgressResponseDto(
    val success: Boolean,
    val data: UserProgressDto,
    val message: String? = null
)

/**
 * 用户进度列表响应
 */
data class UserProgressListResponseDto(
    val success: Boolean,
    val data: List<UserProgressDto>,
    val total: Int,
    val message: String? = null
)

/**
 * 用户进度数据
 */
data class UserProgressDto(
    val userId: String,
    val videoId: String,
    val position: Int,
    val progress: Float,
    val watchCount: Int,
    val isCompleted: Boolean,
    val lastWatchedAt: String?,
    val videoTitle: String?,
    val videoDuration: Int?,
    val categoryTitle: String?,
    val seriesTitle: String?
)

/**
 * 用户缓存响应
 */
data class UserCacheResponseDto(
    val success: Boolean,
    val data: UserCacheDto,
    val message: String? = null
)

/**
 * 用户缓存列表响应
 */
data class UserCacheListResponseDto(
    val success: Boolean,
    val data: List<UserCacheDto>,
    val total: Int,
    val message: String? = null
)

/**
 * 用户缓存数据
 */
data class UserCacheDto(
    val userId: String,
    val videoId: String,
    val isCached: Boolean,
    val localPath: String?,
    val fileSize: Long?,
    val cachedAt: String?
)

/**
 * 用户收藏响应
 */
data class UserFavoritesResponseDto(
    val success: Boolean,
    val data: UserFavoritesDto,
    val message: String? = null
)

/**
 * 用户收藏数据
 */
data class UserFavoritesDto(
    val userId: String,
    val favoriteVideos: List<String>,
    val favoriteCategories: List<String>,
    val favoriteSeries: List<String>,
    val updatedAt: String
)

/**
 * 删除操作响应
 */
data class DeleteResponseDto(
    val success: Boolean,
    val message: String
)

// ========== 废弃的DTO（向后兼容） ==========

@Deprecated("使用 UserProgressUpdateRequestDto 替代")
data class VideoProgressUpdateRequestDto(
    val position: Int,
    val progress: Float
)

@Deprecated("使用 UserProgressResponseDto 替代")
data class VideoProgressResponseDto(
    val success: Boolean,
    val message: String
)

@Deprecated("使用 UserCacheUpdateRequestDto 替代")
data class VideoCacheUpdateRequestDto(
    val isCached: Boolean,
    val localPath: String?
)

@Deprecated("使用 UserCacheResponseDto 替代")
data class VideoCacheResponseDto(
    val success: Boolean,
    val message: String
)

package com.shuimu.course.config

/**
 * API配置常量
 * 统一管理所有API相关的配置和端点
 */
object ApiConfig {
    
    // 基础配置
    const val DEFAULT_BASE_URL = "https://api.shuimu.us.kg"
    const val LOCAL_BASE_URL = "http://********:8001"  // Android模拟器访问本机
    const val LOCALHOST_URL = "http://localhost:8001"   // 真机调试时使用
    
    // 超时配置
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // 分页配置
    const val DEFAULT_PAGE_SIZE = 20
    const val MAX_PAGE_SIZE = 100
    
    // 请求头
    const val HEADER_USER_ID = "X-User-Id"
    const val HEADER_IS_ADMIN = "X-Is-Admin"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_AUTHORIZATION = "Authorization"
    
    // API端点常量
    object Endpoints {
        
        // 用户相关
        const val USERS = "/api/users"
        const val USER_BY_ID = "/api/users/{user_id}"
        const val USER_PROFILE = "/api/users/{user_id}/profile"
        const val USER_PROGRESS = "/api/users/{user_id}/progress"
        const val USER_PROGRESS_VIDEO = "/api/users/{user_id}/progress/{video_id}"
        const val USER_CACHE = "/api/users/{user_id}/cache"
        const val USER_CACHE_VIDEO = "/api/users/{user_id}/cache/{video_id}"
        const val USER_FAVORITES = "/api/users/{user_id}/favorites"
        
        // 系列相关
        const val SERIES = "/api/series"
        const val SERIES_BY_ID = "/api/series/{series_id}"
        const val SERIES_CATEGORIES = "/api/series/{series_id}/categories"
        
        // 分类相关
        const val CATEGORIES = "/api/categories"
        const val CATEGORY_BY_ID = "/api/categories/{category_id}"
        const val CATEGORY_VIDEOS = "/api/categories/{category_id}/videos"
        
        // 视频相关
        const val VIDEOS = "/api/videos"
        const val VIDEO_BY_ID = "/api/videos/{video_id}"
        
        // 认证相关
        const val AUTH_LOGIN = "/api/auth/login"
        const val AUTH_LOGOUT = "/api/auth/logout"
        const val AUTH_REGISTER = "/api/auth/register"
        const val AUTH_REFRESH = "/api/auth/refresh"
        
        // 搜索和其他
        const val SEARCH = "/api/search"
        const val ANALYTICS = "/api/analytics"
        const val HEALTH = "/api/health"
        
        // 废弃的端点（向后兼容，但不推荐使用）
        @Deprecated("使用 USER_PROGRESS_VIDEO 替代")
        const val VIDEO_PROGRESS_DEPRECATED = "/api/videos/{video_id}/progress"
        
        @Deprecated("使用 USER_CACHE_VIDEO 替代")
        const val VIDEO_CACHE_DEPRECATED = "/api/videos/{video_id}/cache-status"
    }
    
    // 响应状态码
    object ResponseCodes {
        const val SUCCESS = 200
        const val CREATED = 201
        const val NO_CONTENT = 204
        const val BAD_REQUEST = 400
        const val UNAUTHORIZED = 401
        const val FORBIDDEN = 403
        const val NOT_FOUND = 404
        const val CONFLICT = 409
        const val INTERNAL_ERROR = 500
    }
    
    // 错误消息
    object ErrorMessages {
        const val NETWORK_ERROR = "网络连接失败"
        const val SERVER_ERROR = "服务器错误"
        const val UNAUTHORIZED = "未授权访问"
        const val FORBIDDEN = "权限不足"
        const val NOT_FOUND = "资源不存在"
        const val TIMEOUT = "请求超时"
        const val UNKNOWN_ERROR = "未知错误"
    }
    
    // 缓存配置
    object Cache {
        const val MAX_SIZE = 50 * 1024 * 1024L  // 50MB
        const val MAX_AGE = 7  // 7天
        const val MAX_STALE = 30  // 30天
    }
    
    // 文件上传配置
    object Upload {
        const val MAX_FILE_SIZE = 50 * 1024 * 1024L  // 50MB
        val ALLOWED_IMAGE_TYPES = listOf("jpg", "jpeg", "png", "gif")
        val ALLOWED_VIDEO_TYPES = listOf("mp4", "avi", "mov", "mkv")
    }
    
    /**
     * 根据环境获取基础URL
     */
    fun getBaseUrl(isDebug: Boolean = false, useLocal: Boolean = false): String {
        return when {
            useLocal -> LOCAL_BASE_URL
            isDebug -> LOCALHOST_URL
            else -> DEFAULT_BASE_URL
        }
    }
    
    /**
     * 构建完整的API URL
     */
    fun buildUrl(baseUrl: String, endpoint: String, vararg params: Pair<String, String>): String {
        var url = "$baseUrl$endpoint"
        params.forEach { (key, value) ->
            url = url.replace("{$key}", value)
        }
        return url
    }
    
    /**
     * 获取用户进度端点URL
     */
    fun getUserProgressUrl(userId: String, videoId: String): String {
        return Endpoints.USER_PROGRESS_VIDEO
            .replace("{user_id}", userId)
            .replace("{video_id}", videoId)
    }
    
    /**
     * 获取用户缓存端点URL
     */
    fun getUserCacheUrl(userId: String, videoId: String): String {
        return Endpoints.USER_CACHE_VIDEO
            .replace("{user_id}", userId)
            .replace("{video_id}", videoId)
    }
    
    /**
     * 获取用户资料端点URL
     */
    fun getUserProfileUrl(userId: String): String {
        return Endpoints.USER_PROFILE.replace("{user_id}", userId)
    }
    
    /**
     * 检查端点是否已废弃
     */
    fun isDeprecatedEndpoint(endpoint: String): Boolean {
        return endpoint.contains("/videos/") && 
               (endpoint.contains("/progress") || endpoint.contains("/cache-status"))
    }
    
    /**
     * 获取推荐的替代端点
     */
    fun getRecommendedEndpoint(deprecatedEndpoint: String, userId: String): String? {
        return when {
            deprecatedEndpoint.contains("/videos/") && deprecatedEndpoint.contains("/progress") -> {
                val videoId = deprecatedEndpoint.substringAfter("/videos/").substringBefore("/progress")
                getUserProgressUrl(userId, videoId)
            }
            deprecatedEndpoint.contains("/videos/") && deprecatedEndpoint.contains("/cache-status") -> {
                val videoId = deprecatedEndpoint.substringAfter("/videos/").substringBefore("/cache-status")
                getUserCacheUrl(userId, videoId)
            }
            else -> null
        }
    }
}

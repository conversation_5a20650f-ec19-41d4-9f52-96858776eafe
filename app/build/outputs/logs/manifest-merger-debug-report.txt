-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:40:9-43:35
REJECTED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:43:13-32
	android:authorities
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:42:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:41:13-67
manifest
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:2:1-47:12
MERGED from [androidx.databinding:databinding-adapters:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0dad27ca493168b73a079d3a4d0504ee\transformed\databinding-adapters-8.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\39086c794708ed20a35300fd0c522e92\transformed\jetified-databinding-ktx-8.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c93ef8c60624fc7749b3d57487972cce\transformed\databinding-runtime-8.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f1d636cd8e49c1757d24ec5015024447\transformed\jetified-viewbinding-8.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ad601f1f1ae6fb89202723eea59af52d\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\95fdb6710668099100d1147d32bf4d24\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\50a9f4044b9bccbf0a8c519c38d7ae77\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\d7dff0166207f8dea4d559657b7fad45\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\08700191ddd2d56fbac450ef3f9e412c\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\baea0717bdf67b8422f16cf082f26ce6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e3234414f5462f8ed798024c6c76c0bc\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6e6cf6fe0fc5cc9789cebf74c6b13b9d\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e6ed7d9d206d328b5e7ed139b606031e\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\60456bf525e41666fc62eb9c2882cd30\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0fc749c2775e5f07cd19114293007dee\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c24a4a0e11cd5adc66fa92578f1d9b2c\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\886ce1740e58ab542309752b74bbc803\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe283503cd6737d4a74acc0a77b9419e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6ea1b802b628a2b3b61ed51dd7a7a335\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e507d9e03ea88ccf901197145909303c\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27e1529af1831858eca56b877be8e174\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4c13bc3f522e74bf85303eafa74f6d7a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\04f482e1fae4e50c48d85cf41084d4f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\6fd029db17aa18707d80d38496f95617\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.palette:palette-ktx:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\70db95e741099acc87d11d0cbd47b63e\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.palette:palette:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\23d33cec19a92451c283f508f6544ca4\transformed\palette-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9ab011938e65b82cee7058e147482cb1\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9066e5e0d69c552d7fb069c70eeb9e2e\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\473a32f8fac81363bbef0a1e3d6b6258\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-work:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\7fa46bef03109f8cb119e0289cc49e54\transformed\jetified-hilt-work-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\014a7426a2e89729c7d4edca92bffa21\transformed\jetified-hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ea56c1847c2f5878975b9fb179e3ff09\transformed\jetified-hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\3223ca6ea0a0397bc3b4b644e8e73c6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\cd6ac6adb8d7693d12b10bfdc63a6a9a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\8a703b662d71cbccfc9389e2e2d56707\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\c322cca812269647392fc064755a2ee1\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\30703bae3d7048ef8d15da95f21d27b5\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d4ed0cbd175cf3646e340348d0fc75e3\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ed624b17e998c266cfbf9ad645cdecae\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a758ffe9ed7e0a42776a90b4b68ada61\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\13e242c26e4c9639fa10efaa752ef518\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\ad952cb150bedcbfcb57f35849ccf9e8\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f8560c85a5abf947eeaf69541e86f016\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d8a069776ede4d477ea69837cfbfaaec\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\756595f22063d63bc7267ad09e370f40\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a926f3e7f8e9b6ea6e1eb83cd87569f3\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d507b493021f24696109726b4ac96304\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dfdf17160d878187184727afc3203245\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\5eaa39e2b01b1a52f80e3fd6a3a12eb2\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\2423e53fcd97975c7034ff4631d4b291\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dd9d72c1543986bad3143b3baf9e812c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\77342f40228e86965c04630dacf2e2d5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.dagger:hilt-android:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\9436a12cefb5e7f2b2d0a45c92afcd50\transformed\jetified-hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] D:\gradle-home\caches\9.0-milestone-1\transforms\a25b72e4fdf8e7f3ee1e9ec6de9234c1\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\14ab925d3b4692b5d1777167637a335d\transformed\jetified-activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\eaa287751c656fb3c53f03256cf2a0ff\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\f277d4b439d17987a0c24ecbff1ba363\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f7952c2c563c31ddfff61888fb83a0e9\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\7b2d924db47edd292db09ef5e0368183\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\2120078e7a259c746da0e8090eb42962\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\89f95748843817fcf2923e892f56b42c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b994445afcf4cb9b32d7707c897f3b79\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6c1d2fd7794310d3a7f72ad22996d968\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\b7f246e56c03d7fd4d06184e83863d36\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3d3f867d5316f3531e2880838def38fb\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\28fb739a5164427010e5bbbb5f54e22c\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\0af80a06c7e76ba44eee8bc57b136dd9\transformed\jetified-lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\fd8de806ac42cfcfa285a52938377f64\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\bde37dd84e7f4aaf09c18adaa37b08a7\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3c077ab40e28b43dbff98cd3bc174cdf\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\2caf3a6959835cf63d764629799f1806\transformed\jetified-lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\1473f9ef607c091c044484ef0f549953\transformed\jetified-lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\23b997f54e04423560edf779f09730ae\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3108475fa557785daea0c10e582a4a91\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\c5924fb23456c14f24bd57e9c7e0ddb3\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\8629166135ab7fb00949a56b321308ee\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\60558d3ccad013c87c458ddc43ef9e3a\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dce41d1ba581375e2178cf3c38b6cf7c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dcbc0df77963565084743a452bdb2270\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\8129f58f16447680d3319eda29d9fa33\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f578dcfc522281a27c8568f2d56ee51b\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe48df94cf495cfa98a567c68b83529e\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\caa8289867611cda4c7a1917f3373d79\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ecc5677eb67afc0693f214b69856ee9e\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\547bef012e77cc394b59f8ba0c78acc3\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\131bf597c71e9b89ec2cc342d77d63f7\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\3c43130ca4373e83e85a5da697fce69f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\bb0e95bf631955d437074d52e9507392\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84f7553502c891ba5df0549853d74279\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4b642b61d290c7a1db170522abbd898a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\67a757d22d20bd6873c712a54d68f3a7\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b81bb2fc572c58bfc9779f1dc79b499d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d884232da9d11e0f3fe3eca406e4f75f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84954566cd0ac0fa48148c54ffb24c33\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\aef89e86ffc841b07e4822d19d5f0ddd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\169d8cdad37496231f480aa2546c1fb6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\183119dccf57fda2da0ca437c55f2a1c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9a0aa36a691e71512fcae0eb75b55e85\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\940daaafc1f736fb7a896a7e23b8fb08\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\36ba34b1eeec3adaf5c09def501dcb26\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\25870ed70101b16dc6ccfc998ffa9664\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\gradle-home\caches\9.0-milestone-1\transforms\68b4a414692aeb7c7661fd40a2f36dd0\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\b41bd94819f1c17536cf2f64ec2d96be\transformed\jetified-dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:5-68
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:5-77
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:5-92
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:22-89
application
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:10:5-45:19
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:10:5-45:19
MERGED from [com.google.android.material:material:1.12.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ad601f1f1ae6fb89202723eea59af52d\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ad601f1f1ae6fb89202723eea59af52d\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0fc749c2775e5f07cd19114293007dee\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0fc749c2775e5f07cd19114293007dee\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\169d8cdad37496231f480aa2546c1fb6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\169d8cdad37496231f480aa2546c1fb6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:14:9-54
	tools:targetApi
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:19:9-29
	android:allowBackup
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:17:9-50
	android:networkSecurityConfig
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:18:9-69
	android:dataExtractionRules
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:13:9-65
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:11:9-42
activity#com.shuimu.course.MainActivity
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:23:13-54
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:27-74
service#com.shuimu.course.player.PlaybackService
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:31:9-38:19
	android:exported
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:33:13-36
	android:foregroundServiceType
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:34:13-58
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:32:13-51
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:35:13-37:29
action#androidx.media3.session.MediaSessionService
ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:17-85
	android:name
		ADDED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:25-83
uses-sdk
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0dad27ca493168b73a079d3a4d0504ee\transformed\databinding-adapters-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0dad27ca493168b73a079d3a4d0504ee\transformed\databinding-adapters-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\39086c794708ed20a35300fd0c522e92\transformed\jetified-databinding-ktx-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\39086c794708ed20a35300fd0c522e92\transformed\jetified-databinding-ktx-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c93ef8c60624fc7749b3d57487972cce\transformed\databinding-runtime-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c93ef8c60624fc7749b3d57487972cce\transformed\databinding-runtime-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f1d636cd8e49c1757d24ec5015024447\transformed\jetified-viewbinding-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f1d636cd8e49c1757d24ec5015024447\transformed\jetified-viewbinding-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ad601f1f1ae6fb89202723eea59af52d\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ad601f1f1ae6fb89202723eea59af52d\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\95fdb6710668099100d1147d32bf4d24\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\95fdb6710668099100d1147d32bf4d24\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\50a9f4044b9bccbf0a8c519c38d7ae77\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\50a9f4044b9bccbf0a8c519c38d7ae77\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\d7dff0166207f8dea4d559657b7fad45\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\d7dff0166207f8dea4d559657b7fad45\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\08700191ddd2d56fbac450ef3f9e412c\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\08700191ddd2d56fbac450ef3f9e412c\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\baea0717bdf67b8422f16cf082f26ce6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\baea0717bdf67b8422f16cf082f26ce6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e3234414f5462f8ed798024c6c76c0bc\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e3234414f5462f8ed798024c6c76c0bc\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6e6cf6fe0fc5cc9789cebf74c6b13b9d\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6e6cf6fe0fc5cc9789cebf74c6b13b9d\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e6ed7d9d206d328b5e7ed139b606031e\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e6ed7d9d206d328b5e7ed139b606031e\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\60456bf525e41666fc62eb9c2882cd30\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\60456bf525e41666fc62eb9c2882cd30\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0fc749c2775e5f07cd19114293007dee\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\0fc749c2775e5f07cd19114293007dee\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c24a4a0e11cd5adc66fa92578f1d9b2c\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c24a4a0e11cd5adc66fa92578f1d9b2c\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\886ce1740e58ab542309752b74bbc803\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\886ce1740e58ab542309752b74bbc803\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe283503cd6737d4a74acc0a77b9419e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe283503cd6737d4a74acc0a77b9419e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6ea1b802b628a2b3b61ed51dd7a7a335\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6ea1b802b628a2b3b61ed51dd7a7a335\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e507d9e03ea88ccf901197145909303c\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e507d9e03ea88ccf901197145909303c\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27e1529af1831858eca56b877be8e174\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27e1529af1831858eca56b877be8e174\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4c13bc3f522e74bf85303eafa74f6d7a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4c13bc3f522e74bf85303eafa74f6d7a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\04f482e1fae4e50c48d85cf41084d4f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\04f482e1fae4e50c48d85cf41084d4f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\6fd029db17aa18707d80d38496f95617\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\6fd029db17aa18707d80d38496f95617\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.palette:palette-ktx:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\70db95e741099acc87d11d0cbd47b63e\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette-ktx:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\70db95e741099acc87d11d0cbd47b63e\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\23d33cec19a92451c283f508f6544ca4\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\23d33cec19a92451c283f508f6544ca4\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9ab011938e65b82cee7058e147482cb1\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9ab011938e65b82cee7058e147482cb1\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9066e5e0d69c552d7fb069c70eeb9e2e\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9066e5e0d69c552d7fb069c70eeb9e2e\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\473a32f8fac81363bbef0a1e3d6b6258\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\473a32f8fac81363bbef0a1e3d6b6258\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\7fa46bef03109f8cb119e0289cc49e54\transformed\jetified-hilt-work-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\7fa46bef03109f8cb119e0289cc49e54\transformed\jetified-hilt-work-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\014a7426a2e89729c7d4edca92bffa21\transformed\jetified-hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\014a7426a2e89729c7d4edca92bffa21\transformed\jetified-hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ea56c1847c2f5878975b9fb179e3ff09\transformed\jetified-hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\ea56c1847c2f5878975b9fb179e3ff09\transformed\jetified-hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\3223ca6ea0a0397bc3b4b644e8e73c6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\3223ca6ea0a0397bc3b4b644e8e73c6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\cd6ac6adb8d7693d12b10bfdc63a6a9a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\cd6ac6adb8d7693d12b10bfdc63a6a9a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\8a703b662d71cbccfc9389e2e2d56707\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\8a703b662d71cbccfc9389e2e2d56707\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\c322cca812269647392fc064755a2ee1\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\c322cca812269647392fc064755a2ee1\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\30703bae3d7048ef8d15da95f21d27b5\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] D:\gradle-home\caches\9.0-milestone-1\transforms\30703bae3d7048ef8d15da95f21d27b5\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d4ed0cbd175cf3646e340348d0fc75e3\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d4ed0cbd175cf3646e340348d0fc75e3\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ed624b17e998c266cfbf9ad645cdecae\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ed624b17e998c266cfbf9ad645cdecae\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a758ffe9ed7e0a42776a90b4b68ada61\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a758ffe9ed7e0a42776a90b4b68ada61\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\13e242c26e4c9639fa10efaa752ef518\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\13e242c26e4c9639fa10efaa752ef518\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\ad952cb150bedcbfcb57f35849ccf9e8\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\ad952cb150bedcbfcb57f35849ccf9e8\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f8560c85a5abf947eeaf69541e86f016\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f8560c85a5abf947eeaf69541e86f016\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d8a069776ede4d477ea69837cfbfaaec\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d8a069776ede4d477ea69837cfbfaaec\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\756595f22063d63bc7267ad09e370f40\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\756595f22063d63bc7267ad09e370f40\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a926f3e7f8e9b6ea6e1eb83cd87569f3\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\a926f3e7f8e9b6ea6e1eb83cd87569f3\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d507b493021f24696109726b4ac96304\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\d507b493021f24696109726b4ac96304\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dfdf17160d878187184727afc3203245\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dfdf17160d878187184727afc3203245\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\5eaa39e2b01b1a52f80e3fd6a3a12eb2\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\5eaa39e2b01b1a52f80e3fd6a3a12eb2\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\2423e53fcd97975c7034ff4631d4b291\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\2423e53fcd97975c7034ff4631d4b291\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dd9d72c1543986bad3143b3baf9e812c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\dd9d72c1543986bad3143b3baf9e812c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\77342f40228e86965c04630dacf2e2d5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\77342f40228e86965c04630dacf2e2d5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\9436a12cefb5e7f2b2d0a45c92afcd50\transformed\jetified-hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\9436a12cefb5e7f2b2d0a45c92afcd50\transformed\jetified-hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] D:\gradle-home\caches\9.0-milestone-1\transforms\a25b72e4fdf8e7f3ee1e9ec6de9234c1\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] D:\gradle-home\caches\9.0-milestone-1\transforms\a25b72e4fdf8e7f3ee1e9ec6de9234c1\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\14ab925d3b4692b5d1777167637a335d\transformed\jetified-activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\14ab925d3b4692b5d1777167637a335d\transformed\jetified-activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\eaa287751c656fb3c53f03256cf2a0ff\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\eaa287751c656fb3c53f03256cf2a0ff\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\f277d4b439d17987a0c24ecbff1ba363\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\f277d4b439d17987a0c24ecbff1ba363\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f7952c2c563c31ddfff61888fb83a0e9\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\f7952c2c563c31ddfff61888fb83a0e9\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\7b2d924db47edd292db09ef5e0368183\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\7b2d924db47edd292db09ef5e0368183\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\2120078e7a259c746da0e8090eb42962\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\2120078e7a259c746da0e8090eb42962\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\89f95748843817fcf2923e892f56b42c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\89f95748843817fcf2923e892f56b42c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b994445afcf4cb9b32d7707c897f3b79\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b994445afcf4cb9b32d7707c897f3b79\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6c1d2fd7794310d3a7f72ad22996d968\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\6c1d2fd7794310d3a7f72ad22996d968\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\b7f246e56c03d7fd4d06184e83863d36\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\gradle-home\caches\9.0-milestone-1\transforms\b7f246e56c03d7fd4d06184e83863d36\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3d3f867d5316f3531e2880838def38fb\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3d3f867d5316f3531e2880838def38fb\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\28fb739a5164427010e5bbbb5f54e22c\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\28fb739a5164427010e5bbbb5f54e22c\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\0af80a06c7e76ba44eee8bc57b136dd9\transformed\jetified-lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\0af80a06c7e76ba44eee8bc57b136dd9\transformed\jetified-lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\fd8de806ac42cfcfa285a52938377f64\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\fd8de806ac42cfcfa285a52938377f64\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\bde37dd84e7f4aaf09c18adaa37b08a7\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\bde37dd84e7f4aaf09c18adaa37b08a7\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3c077ab40e28b43dbff98cd3bc174cdf\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3c077ab40e28b43dbff98cd3bc174cdf\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\2caf3a6959835cf63d764629799f1806\transformed\jetified-lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\2caf3a6959835cf63d764629799f1806\transformed\jetified-lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\1473f9ef607c091c044484ef0f549953\transformed\jetified-lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\1473f9ef607c091c044484ef0f549953\transformed\jetified-lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\23b997f54e04423560edf779f09730ae\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\23b997f54e04423560edf779f09730ae\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3108475fa557785daea0c10e582a4a91\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\3108475fa557785daea0c10e582a4a91\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\c5924fb23456c14f24bd57e9c7e0ddb3\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\c5924fb23456c14f24bd57e9c7e0ddb3\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\8629166135ab7fb00949a56b321308ee\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\8629166135ab7fb00949a56b321308ee\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\60558d3ccad013c87c458ddc43ef9e3a\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\60558d3ccad013c87c458ddc43ef9e3a\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dce41d1ba581375e2178cf3c38b6cf7c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dce41d1ba581375e2178cf3c38b6cf7c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dcbc0df77963565084743a452bdb2270\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\gradle-home\caches\9.0-milestone-1\transforms\dcbc0df77963565084743a452bdb2270\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\8129f58f16447680d3319eda29d9fa33\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\8129f58f16447680d3319eda29d9fa33\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f578dcfc522281a27c8568f2d56ee51b\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\gradle-home\caches\9.0-milestone-1\transforms\f578dcfc522281a27c8568f2d56ee51b\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe48df94cf495cfa98a567c68b83529e\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\fe48df94cf495cfa98a567c68b83529e\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\caa8289867611cda4c7a1917f3373d79\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\caa8289867611cda4c7a1917f3373d79\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ecc5677eb67afc0693f214b69856ee9e\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\ecc5677eb67afc0693f214b69856ee9e\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\547bef012e77cc394b59f8ba0c78acc3\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\547bef012e77cc394b59f8ba0c78acc3\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\131bf597c71e9b89ec2cc342d77d63f7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\131bf597c71e9b89ec2cc342d77d63f7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\3c43130ca4373e83e85a5da697fce69f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\3c43130ca4373e83e85a5da697fce69f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\bb0e95bf631955d437074d52e9507392\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\bb0e95bf631955d437074d52e9507392\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84f7553502c891ba5df0549853d74279\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84f7553502c891ba5df0549853d74279\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4b642b61d290c7a1db170522abbd898a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\4b642b61d290c7a1db170522abbd898a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\67a757d22d20bd6873c712a54d68f3a7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\gradle-home\caches\9.0-milestone-1\transforms\67a757d22d20bd6873c712a54d68f3a7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b81bb2fc572c58bfc9779f1dc79b499d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\b81bb2fc572c58bfc9779f1dc79b499d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d884232da9d11e0f3fe3eca406e4f75f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\gradle-home\caches\9.0-milestone-1\transforms\d884232da9d11e0f3fe3eca406e4f75f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84954566cd0ac0fa48148c54ffb24c33\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\gradle-home\caches\9.0-milestone-1\transforms\84954566cd0ac0fa48148c54ffb24c33\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98dd091f7001d413b6166f2f68840855\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\aef89e86ffc841b07e4822d19d5f0ddd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\aef89e86ffc841b07e4822d19d5f0ddd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\169d8cdad37496231f480aa2546c1fb6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\gradle-home\caches\9.0-milestone-1\transforms\169d8cdad37496231f480aa2546c1fb6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\183119dccf57fda2da0ca437c55f2a1c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\183119dccf57fda2da0ca437c55f2a1c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9a0aa36a691e71512fcae0eb75b55e85\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\9a0aa36a691e71512fcae0eb75b55e85\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\940daaafc1f736fb7a896a7e23b8fb08\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\940daaafc1f736fb7a896a7e23b8fb08\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\36ba34b1eeec3adaf5c09def501dcb26\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\36ba34b1eeec3adaf5c09def501dcb26\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\25870ed70101b16dc6ccfc998ffa9664\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\gradle-home\caches\9.0-milestone-1\transforms\25870ed70101b16dc6ccfc998ffa9664\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\gradle-home\caches\9.0-milestone-1\transforms\68b4a414692aeb7c7661fd40a2f36dd0\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\gradle-home\caches\9.0-milestone-1\transforms\68b4a414692aeb7c7661fd40a2f36dd0\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\b41bd94819f1c17536cf2f64ec2d96be\transformed\jetified-dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] D:\gradle-home\caches\9.0-milestone-1\transforms\b41bd94819f1c17536cf2f64ec2d96be\transformed\jetified-dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\01-shuimu_01\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e6ed7d9d206d328b5e7ed139b606031e\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\e6ed7d9d206d328b5e7ed139b606031e\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\gradle-home\caches\9.0-milestone-1\transforms\27c3afebe8b0a1e1ad72c7e860c5eaf2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] D:\gradle-home\caches\9.0-milestone-1\transforms\998d6b0a7add346804f88a20f50a1e3f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92

<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\src\main\res"><file name="ic_chevron_down" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_chevron_down.xml" qualifiers="" type="drawable"/><file name="ic_comment" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_comment.xml" qualifiers="" type="drawable"/><file name="ic_comments_fa" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_comments_fa.xml" qualifiers="" type="drawable"/><file name="ic_crown" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_crown.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_play_circle" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_play_circle.xml" qualifiers="" type="drawable"/><file name="ic_water_fa" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_water_fa.xml" qualifiers="" type="drawable"/><file name="ic_water_wave" path="D:\01-shuimu_01\app\src\main\res\drawable\ic_water_wave.xml" qualifiers="" type="drawable"/><file path="D:\01-shuimu_01\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\01-shuimu_01\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">水幕视频课程App</string></file><file path="D:\01-shuimu_01\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.ShuimuCourse" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryVariant">@color/black</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/black</item>
        <item name="colorSecondaryVariant">@color/black</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
    </style></file><file name="backup_rules" path="D:\01-shuimu_01\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\01-shuimu_01\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\01-shuimu_01\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\01-shuimu_01\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
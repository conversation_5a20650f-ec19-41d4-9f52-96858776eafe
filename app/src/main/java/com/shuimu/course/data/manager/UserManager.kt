package com.shuimu.course.data.manager

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户管理器
 * 负责管理当前用户信息和状态
 */
@Singleton
class UserManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "user_prefs", Context.MODE_PRIVATE
    )
    
    companion object {
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_EMAIL = "email"
        private const val KEY_IS_ADMIN = "is_admin"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val DEFAULT_USER_ID = "user_001" // 默认用户ID，用于测试
    }
    
    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(): String {
        return prefs.getString(KEY_USER_ID, DEFAULT_USER_ID) ?: DEFAULT_USER_ID
    }
    
    /**
     * 设置当前用户ID
     */
    fun setCurrentUserId(userId: String) {
        prefs.edit().putString(KEY_USER_ID, userId).apply()
    }
    
    /**
     * 获取当前用户名
     */
    fun getCurrentUsername(): String? {
        return prefs.getString(KEY_USERNAME, null)
    }
    
    /**
     * 设置当前用户名
     */
    fun setCurrentUsername(username: String) {
        prefs.edit().putString(KEY_USERNAME, username).apply()
    }
    
    /**
     * 获取当前用户邮箱
     */
    fun getCurrentUserEmail(): String? {
        return prefs.getString(KEY_EMAIL, null)
    }
    
    /**
     * 设置当前用户邮箱
     */
    fun setCurrentUserEmail(email: String) {
        prefs.edit().putString(KEY_EMAIL, email).apply()
    }
    
    /**
     * 检查当前用户是否是管理员
     */
    fun isCurrentUserAdmin(): Boolean {
        return prefs.getBoolean(KEY_IS_ADMIN, false)
    }
    
    /**
     * 设置当前用户管理员状态
     */
    fun setCurrentUserAdmin(isAdmin: Boolean) {
        prefs.edit().putBoolean(KEY_IS_ADMIN, isAdmin).apply()
    }
    
    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }
    
    /**
     * 设置用户登录状态
     */
    fun setLoggedIn(isLoggedIn: Boolean) {
        prefs.edit().putBoolean(KEY_IS_LOGGED_IN, isLoggedIn).apply()
    }
    
    /**
     * 登录用户
     */
    fun loginUser(userId: String, username: String, email: String, isAdmin: Boolean = false) {
        prefs.edit().apply {
            putString(KEY_USER_ID, userId)
            putString(KEY_USERNAME, username)
            putString(KEY_EMAIL, email)
            putBoolean(KEY_IS_ADMIN, isAdmin)
            putBoolean(KEY_IS_LOGGED_IN, true)
        }.apply()
    }
    
    /**
     * 登出用户
     */
    fun logoutUser() {
        prefs.edit().apply {
            remove(KEY_USER_ID)
            remove(KEY_USERNAME)
            remove(KEY_EMAIL)
            remove(KEY_IS_ADMIN)
            putBoolean(KEY_IS_LOGGED_IN, false)
        }.apply()
    }
    
    /**
     * 清除所有用户数据
     */
    fun clearUserData() {
        prefs.edit().clear().apply()
    }
    
    /**
     * 获取用户认证头信息
     */
    fun getAuthHeaders(): Map<String, String> {
        return mapOf(
            "X-User-Id" to getCurrentUserId(),
            "X-Is-Admin" to isCurrentUserAdmin().toString()
        )
    }
}

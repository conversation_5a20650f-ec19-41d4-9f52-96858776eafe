from fastapi import APIRouter, HTTPException, Header, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
from pathlib import Path
import json

# 导入用户数据处理函数
from ..utils.user_data import (
    load_user_data,
    save_user_data,
    get_video_watch_progress,
    update_video_watch_progress,
    get_video_cache_status,
    update_video_cache_status
)

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    """加载JSON数据"""
    try:
        with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_data(file_path, data):
    """保存JSON数据"""
    with open(DATA_DIR / file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

# 权限验证依赖
def get_current_user_id(x_user_id: Optional[str] = Header("user_001")):
    """获取当前用户ID"""
    if not x_user_id:
        raise HTTPException(status_code=401, detail="User authentication required")
    return x_user_id

def get_admin_status(x_is_admin: Optional[str] = Header(None)):
    """获取管理员状态"""
    return x_is_admin == "true"

def verify_user_data_permission(current_user_id: str, target_user_id: str, is_admin: bool = False):
    """验证用户数据访问权限"""
    if is_admin:
        return True  # 管理员可以操作任何用户数据
    if current_user_id == target_user_id:
        return True  # 用户可以操作自己的数据
    raise HTTPException(status_code=403, detail="Permission denied: Cannot access other user's data")

# 数据模型
class UserProgressUpdateRequest(BaseModel):
    position: Optional[int] = None
    progress: Optional[float] = None
    watchCount: Optional[int] = None
    isCompleted: Optional[bool] = None
    lastWatchedAt: Optional[str] = None

class UserCacheUpdateRequest(BaseModel):
    isCached: Optional[bool] = None
    localPath: Optional[str] = None
    fileSize: Optional[int] = None
    cachedAt: Optional[str] = None

class UserSettingsUpdateRequest(BaseModel):
    autoPlay: Optional[bool] = None
    playbackSpeed: Optional[float] = None
    subtitles: Optional[bool] = None
    downloadQuality: Optional[str] = None
    notifications: Optional[Dict[str, bool]] = None

class UserFavoriteUpdateRequest(BaseModel):
    type: str  # video, category, series
    itemId: str
    action: str  # add, remove

class UserStatsUpdateRequest(BaseModel):
    totalWatchTime: Optional[int] = None
    streakDays: Optional[int] = None
    totalDaysActive: Optional[int] = None
    lastActiveDate: Optional[str] = None

# 用户观看进度端点
@router.put("/users/{user_id}/progress/{video_id}")
def update_user_progress(
    user_id: str,
    video_id: str,
    update_data: UserProgressUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户观看进度（统一端点）"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)
        
        # 获取视频信息验证
        videos_db = load_data("videos.json")
        video = next((v for v in videos_db if v["id"] == video_id), None)
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # 更新进度数据
        duration = video.get("duration", 0)
        
        if update_data.position is not None:
            # 更新观看位置
            update_video_watch_progress(user_id, video_id, update_data.position, duration)
            progress_percentage = (update_data.position / duration * 100) if duration > 0 else 0
        else:
            # 获取当前进度
            progress_data = get_video_watch_progress(user_id, video_id)
            progress_percentage = progress_data.get("progress", 0)
        
        # 处理观看次数更新
        if update_data.watchCount is not None:
            current_progress = get_video_watch_progress(user_id, video_id)
            position = current_progress.get("position", 0)
            update_video_watch_progress(user_id, video_id, position, duration, update_data.watchCount)
        
        return {
            "success": True,
            "message": f"Progress for video '{video_id}' of user '{user_id}' updated successfully",
            "data": {
                "videoId": video_id,
                "userId": user_id,
                "position": update_data.position,
                "progress": progress_percentage,
                "watchCount": update_data.watchCount
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Progress Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

# 用户缓存状态端点
@router.put("/users/{user_id}/cache/{video_id}")
def update_user_cache(
    user_id: str,
    video_id: str,
    update_data: UserCacheUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户缓存状态（统一端点）"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)
        
        # 获取视频信息验证
        videos_db = load_data("videos.json")
        video = next((v for v in videos_db if v["id"] == video_id), None)
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # 更新缓存状态
        if update_data.isCached is not None:
            update_video_cache_status(
                user_id=user_id,
                video_id=video_id,
                is_cached=update_data.isCached,
                local_path=update_data.localPath
            )
        
        return {
            "success": True,
            "message": f"Cache status for video '{video_id}' of user '{user_id}' updated successfully",
            "data": {
                "videoId": video_id,
                "userId": user_id,
                "isCached": update_data.isCached,
                "localPath": update_data.localPath
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Cache Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

# 用户设置端点
@router.put("/users/{user_id}/settings")
def update_user_settings(
    user_id: str,
    update_data: UserSettingsUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户设置（统一端点）"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)
        
        # 加载用户设置数据
        settings_db = load_data("user_settings.json")
        
        # 查找用户设置
        user_settings = None
        user_index = None
        for i, user_set in enumerate(settings_db):
            if user_set["userId"] == user_id:
                user_settings = user_set
                user_index = i
                break
        
        # 如果用户设置不存在，创建新的
        if user_settings is None:
            new_user_settings = {
                "userId": user_id,
                "settings": {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            settings_db.append(new_user_settings)
            user_index = len(settings_db) - 1
            user_settings = new_user_settings
        
        # 更新设置数据
        settings_data = user_settings["settings"].copy()
        
        # 应用更新
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            if key == "notifications" and isinstance(value, dict):
                # 处理嵌套的通知对象
                if "notifications" not in settings_data:
                    settings_data["notifications"] = {}
                settings_data["notifications"].update(value)
            else:
                settings_data[key] = value
        
        # 更新内存中的数据
        settings_db[user_index]["settings"] = settings_data
        settings_db[user_index]["updated_at"] = datetime.now().isoformat()
        
        # 保存到文件
        save_data("user_settings.json", settings_db)
        
        return {
            "success": True,
            "message": f"Settings for user '{user_id}' updated successfully",
            "data": settings_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Settings Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

# 用户收藏端点
@router.put("/users/{user_id}/favorites")
def update_user_favorites(
    user_id: str,
    update_data: UserFavoriteUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户收藏（统一端点）"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)
        
        # 加载用户收藏数据
        favorites_db = load_data("user_favorites.json")
        
        # 查找用户收藏
        user_favorites = None
        user_index = None
        for i, user_fav in enumerate(favorites_db):
            if user_fav["userId"] == user_id:
                user_favorites = user_fav
                user_index = i
                break
        
        # 如果用户收藏不存在，创建新的
        if user_favorites is None:
            new_user_favorites = {
                "userId": user_id,
                "favorites": [],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            favorites_db.append(new_user_favorites)
            user_index = len(favorites_db) - 1
            user_favorites = new_user_favorites
        
        # 处理收藏操作
        favorites_list = user_favorites["favorites"]
        favorite_item = {
            "type": update_data.type,
            "itemId": update_data.itemId,
            "favoritedAt": datetime.now().isoformat()
        }
        
        if update_data.action == "add":
            # 添加收藏（避免重复）
            existing = next((f for f in favorites_list if f["type"] == update_data.type and f["itemId"] == update_data.itemId), None)
            if not existing:
                favorites_list.append(favorite_item)
        elif update_data.action == "remove":
            # 移除收藏
            favorites_list[:] = [f for f in favorites_list if not (f["type"] == update_data.type and f["itemId"] == update_data.itemId)]
        
        # 更新内存中的数据
        favorites_db[user_index]["favorites"] = favorites_list
        favorites_db[user_index]["updated_at"] = datetime.now().isoformat()
        
        # 保存到文件
        save_data("user_favorites.json", favorites_db)
        
        return {
            "success": True,
            "message": f"Favorites for user '{user_id}' updated successfully",
            "data": {
                "action": update_data.action,
                "type": update_data.type,
                "itemId": update_data.itemId,
                "totalFavorites": len(favorites_list)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Favorites Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

# 新增GET端点
@router.get("/users/{user_id}/progress")
def get_user_all_progress(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """获取用户所有观看进度"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        # 从数据库获取用户所有进度
        from ..database.mysql_manager import mysql_manager

        progress_data = mysql_manager.execute_query("""
            SELECT uvp.video_id, uvp.watch_position as position,
                   (uvp.watch_position / COALESCE(v.duration, 1)) as progress,
                   uvp.watch_count, uvp.is_completed,
                   uvp.last_watch_date as last_watched_at, uvp.created_at,
                   v.title as video_title, v.duration as video_duration,
                   c.title as category_title, s.title as series_title
            FROM user_video_progress uvp
            LEFT JOIN videos v ON uvp.video_id = v.id
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE uvp.user_id = %s
            ORDER BY uvp.last_watch_date DESC
        """, (user_id,))

        # 转换时间格式
        for progress in progress_data:
            if progress['last_watched_at']:
                progress['last_watched_at'] = progress['last_watched_at'].isoformat()
            if progress['created_at']:
                progress['created_at'] = progress['created_at'].isoformat()

        return {
            "success": True,
            "data": progress_data,
            "total": len(progress_data)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户进度失败: {str(e)}")

@router.get("/users/{user_id}/progress/{video_id}")
def get_user_video_progress(
    user_id: str,
    video_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """获取用户特定视频的观看进度"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        from ..database.mysql_manager import mysql_manager

        progress = mysql_manager.execute_query("""
            SELECT uvp.video_id, uvp.watch_position as position,
                   uvp.watch_count, uvp.is_completed,
                   uvp.last_watch_date as last_watched_at, uvp.created_at,
                   v.title as video_title, v.duration as video_duration,
                   (uvp.watch_position / COALESCE(NULLIF(v.duration, 0), 1)) as progress
            FROM user_video_progress uvp
            LEFT JOIN videos v ON uvp.video_id = v.id
            WHERE uvp.user_id = %s AND uvp.video_id = %s
        """, (user_id, video_id))

        if not progress:
            return {
                "success": True,
                "data": {
                    "user_id": user_id,
                    "video_id": video_id,
                    "position": 0,
                    "progress": 0.0,
                    "watch_count": 0,
                    "is_completed": False,
                    "last_watched_at": None
                }
            }

        progress_data = progress[0]
        if progress_data['last_watched_at']:
            progress_data['last_watched_at'] = progress_data['last_watched_at'].isoformat()
        if progress_data['created_at']:
            progress_data['created_at'] = progress_data['created_at'].isoformat()

        return {
            "success": True,
            "data": progress_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频进度失败: {str(e)}")

@router.get("/users/{user_id}/cache")
def get_user_cache_list(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """获取用户缓存列表"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        # 从JSON文件加载缓存数据
        cache_db = load_data("user_cache.json")

        user_cache = []
        for cache_item in cache_db:
            if cache_item.get("userId") == user_id:
                user_cache.append(cache_item)

        return {
            "success": True,
            "data": user_cache,
            "total": len(user_cache)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户缓存列表失败: {str(e)}")

@router.get("/users/{user_id}/favorites")
def get_user_favorites(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """获取用户收藏列表"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        # 从JSON文件加载收藏数据
        favorites_db = load_data("user_favorites.json")

        user_favorites = None
        for fav in favorites_db:
            if fav.get("userId") == user_id:
                user_favorites = fav
                break

        if not user_favorites:
            user_favorites = {
                "userId": user_id,
                "favoriteVideos": [],
                "favoriteCategories": [],
                "favoriteSeries": [],
                "updatedAt": datetime.now().isoformat()
            }

        return {
            "success": True,
            "data": user_favorites
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户收藏失败: {str(e)}")

# 新增DELETE端点
@router.delete("/users/{user_id}/progress/{video_id}")
def delete_user_video_progress(
    user_id: str,
    video_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """删除用户视频观看进度"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        from ..database.mysql_manager import mysql_manager

        # 检查进度记录是否存在
        existing_progress = mysql_manager.execute_query("""
            SELECT id FROM user_video_progress WHERE user_id = %s AND video_id = %s
        """, (user_id, video_id))

        if not existing_progress:
            raise HTTPException(status_code=404, detail="进度记录不存在")

        # 删除进度记录
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM user_video_progress WHERE user_id = %s AND video_id = %s
        """, (user_id, video_id))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "进度记录删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除进度记录失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除进度记录失败: {str(e)}")

@router.delete("/users/{user_id}/cache/{video_id}")
def delete_user_video_cache(
    user_id: str,
    video_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """删除用户视频缓存记录"""
    try:
        # 权限验证
        verify_user_data_permission(current_user_id, user_id, is_admin)

        # 从JSON文件加载缓存数据
        cache_db = load_data("user_cache.json")

        # 查找并删除缓存记录
        cache_found = False
        for i, cache_item in enumerate(cache_db):
            if (cache_item.get("userId") == user_id and
                cache_item.get("videoId") == video_id):
                cache_db.pop(i)
                cache_found = True
                break

        if not cache_found:
            raise HTTPException(status_code=404, detail="缓存记录不存在")

        # 保存更新后的数据
        save_data("user_cache.json", cache_db)

        return {
            "success": True,
            "message": "缓存记录删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除缓存记录失败: {str(e)}")

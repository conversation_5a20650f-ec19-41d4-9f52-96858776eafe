{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad952cb150bedcbfcb57f35849ccf9e8\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "269,270", "startColumns": "4,4", "startOffsets": "22961,23047", "endColumns": "85,85", "endOffsets": "23042,23128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1059,1164,1271,1356,1460,1580,1658,1734,1826,1920,2015,2109,2209,2303,2399,2494,2586,2678,2760,2871,2974,3073,3188,3302,3405,3560,21977", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "1054,1159,1266,1351,1455,1575,1653,1729,1821,1915,2010,2104,2204,2298,2394,2489,2581,2673,2755,2866,2969,3068,3183,3297,3400,3555,3658,22055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\98772e44dd82187f64bd43b721368829\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7865,7936,8000,8064,8131,8208,8277,8366,8449", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "7931,7995,8059,8126,8203,8272,8361,8444,8516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ed624b17e998c266cfbf9ad645cdecae\\transformed\\jetified-material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10105,10227,10344,10465,10583,10683,10781,10896,11048,11169,11311,11396,11495,11591,11694,11812,11933,12037,12168,12296,12432,12610,12741,12861,12982,13117,13214,13314,13434,13563,13663,13770,13873,14010,14150,14256,14360,14444,14544,14641,14752,14839,14926,15031,15111,15194,15293,15397,15492,15591,15679,15789,15890,15995,16115,16195,16296", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "10222,10339,10460,10578,10678,10776,10891,11043,11164,11306,11391,11490,11586,11689,11807,11928,12032,12163,12291,12427,12605,12736,12856,12977,13112,13209,13309,13429,13558,13658,13765,13868,14005,14145,14251,14355,14439,14539,14636,14747,14834,14921,15026,15106,15189,15288,15392,15487,15586,15674,15784,15885,15990,16110,16190,16291,16386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3735,3821,3903,3980,4078,4906,5003,5125,5744,5804,9702,9791,10042,16391,16484,16546,16612,16670,16743,16807,16863,16985,17042,17104,17160,17236,17878,17963,18042,18140,18226,18312,18450,18531,18610,18734,18824,18901,18958,19009,19075,19153,19236,19307,19383,19458,19537,19610,19681,19790,19884,19962,20051,20141,20215,20296,20383,20436,20515,20582,20663,20747,20809,20873,20936,21007,21115,21227,21329,21440,21501,21732,22060,22143,22296", "endLines": "22,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "936,3816,3898,3975,4073,4167,4998,5120,5201,5799,5863,9786,9865,10100,16479,16541,16607,16665,16738,16802,16858,16980,17037,17099,17155,17231,17365,17958,18037,18135,18221,18307,18445,18526,18605,18729,18819,18896,18953,19004,19070,19148,19231,19302,19378,19453,19532,19605,19676,19785,19879,19957,20046,20136,20210,20291,20378,20431,20510,20577,20658,20742,20804,20868,20931,21002,21110,21222,21324,21435,21496,21551,21808,22138,22214,22363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60558d3ccad013c87c458ddc43ef9e3a\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,990,1073,1150,1225,1297,1368,1452,1522", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,985,1068,1145,1220,1292,1363,1447,1517,1637"}, "to": {"startLines": "66,67,68,70,71,128,129,252,253,255,256,260,262,263,264,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5206,5298,5381,5560,5659,9870,9946,21556,21643,21813,21894,22219,22368,22443,22515,22687,22771,22841", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "5293,5376,5473,5654,5739,9941,10037,21638,21727,21889,21972,22291,22438,22510,22581,22766,22836,22956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\50a9f4044b9bccbf0a8c519c38d7ae77\\transformed\\jetified-media3-ui-1.3.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3401,3467,3519,3581,3657,3733,3788", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54,66", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3396,3462,3514,3576,3652,3728,3783,3850"}, "to": {"startLines": "2,11,15,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,581,5868,5957,6045,6125,6218,6311,6384,6451,6553,6651,6719,6786,6851,6920,7039,7158,7273,7346,7425,7500,7569,7652,7734,7800,8521,8574,8632,8680,8741,8806,8868,8933,9001,9059,9117,9183,9248,9314,9366,9428,9504,9580,9635", "endLines": "10,14,18,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54,66", "endOffsets": "376,576,767,5952,6040,6120,6213,6306,6379,6446,6548,6646,6714,6781,6846,6915,7034,7153,7268,7341,7420,7495,7564,7647,7729,7795,7860,8569,8627,8675,8736,8801,8863,8928,8996,9054,9112,9178,9243,9309,9361,9423,9499,9575,9630,9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\c12dcf7643131f54deb56aa398bbca93\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "56,57,58,59,60,61,62,265", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4172,4274,4376,4476,4576,4683,4787,22586", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "4269,4371,4471,4571,4678,4782,4901,22682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60456bf525e41666fc62eb9c2882cd30\\transformed\\jetified-media3-session-1.3.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,127,209,280,350,435,521,618", "endColumns": "71,81,70,69,84,85,96,98", "endOffsets": "122,204,275,345,430,516,613,712"}, "to": {"startLines": "50,69,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3663,5478,17370,17441,17511,17596,17682,17779", "endColumns": "71,81,70,69,84,85,96,98", "endOffsets": "3730,5555,17436,17506,17591,17677,17774,17873"}}]}]}
package com.shuimu.course.di

import android.content.Context
import com.google.gson.Gson
import com.shuimu.course.BuildConfig
import com.shuimu.course.data.remote.api.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import com.shuimu.course.data.remote.interceptors.UserIdInterceptor
import okhttp3.Dns
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Singleton
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.HttpsURLConnection

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    // 从 BuildConfig 读取配置化常量，避免硬编码
    private val BASE_URL = BuildConfig.API_BASE_URL
    private val HOST_NAME = BuildConfig.API_HOST_NAME
    private val SERVER_IP = BuildConfig.API_SERVER_IP

    @Provides
    @Singleton
    fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY)
    }

    @Provides
    @Singleton
    fun provideHostInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request().newBuilder()
                .addHeader("Host", HOST_NAME)
                .build()
            chain.proceed(request)
        }
    }

    @Provides
    @Singleton
    fun provideHostnameVerifier(): HostnameVerifier {
        return HostnameVerifier { hostname, session ->
            if (hostname == SERVER_IP) {
                HttpsURLConnection.getDefaultHostnameVerifier().verify(HOST_NAME, session)
            } else {
                HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session)
            }
        }
    }

    /**
     * 自定义 DNS：当系统 DNS 无法解析时，直接把域名映射到固定 IP，
     * 其余域名仍交给系统解析。这样既保留 HTTPS 域名校验，又避免 UnknownHostException。
     */
    @Provides
    @Singleton
    fun provideCustomDns(): Dns = object : Dns {
        override fun lookup(hostname: String): List<java.net.InetAddress> {
            return if (hostname == HOST_NAME) {
                listOf(java.net.InetAddress.getByName(SERVER_IP))
            } else {
                Dns.SYSTEM.lookup(hostname)
            }
        }
    }

    @Provides
    @Singleton
    fun provideOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        hostInterceptor: Interceptor,
        hostnameVerifier: HostnameVerifier,
        customDns: Dns,
        userIdInterceptor: UserIdInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(userIdInterceptor)
            .addInterceptor(hostInterceptor)
            .addInterceptor(loggingInterceptor)
            .hostnameVerifier(hostnameVerifier)
            .dns(customDns)
            .build()
    }

    @Provides
    @Singleton
    fun provideGson(): Gson {
        return Gson()
    }

    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient, gson: Gson): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    @Provides
    @Singleton
    fun provideAuthApi(retrofit: Retrofit): AuthApi = retrofit.create(AuthApi::class.java)

    @Provides
    @Singleton
    fun provideSeriesApi(retrofit: Retrofit): SeriesApi = retrofit.create(SeriesApi::class.java)

    @Provides
    @Singleton
    fun provideVideoApi(retrofit: Retrofit): VideoApi = retrofit.create(VideoApi::class.java)

    @Provides
    @Singleton
    fun providePaymentApi(retrofit: Retrofit): PaymentApi = retrofit.create(PaymentApi::class.java)

    @Provides
    @Singleton
    fun provideUserApi(retrofit: Retrofit): UserApi = retrofit.create(UserApi::class.java)

    @Provides
    @Singleton
    fun provideUserDataApi(retrofit: Retrofit): UserDataApi = retrofit.create(UserDataApi::class.java)

    @Provides
    @Singleton
    fun provideShareApi(retrofit: Retrofit): ShareApi = retrofit.create(ShareApi::class.java)

    @Provides
    @Singleton
    fun provideSearchApi(retrofit: Retrofit): SearchApi = retrofit.create(SearchApi::class.java)

    @Provides
    @Singleton
    fun providePlaylistApi(retrofit: Retrofit): PlaylistApi = retrofit.create(PlaylistApi::class.java)

    @Provides
    @Singleton
    fun provideCacheApi(retrofit: Retrofit): CacheApi = retrofit.create(CacheApi::class.java)
} 
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api import auth, series, categories, videos, payments, users, share, analytics, search, playlist, products, cache, admin_series, admin_videos, admin_users, user_data, users_crud

app = FastAPI(title="Shuimu Video Course Mock API")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(auth.router, prefix="/api", tags=["Authentication"])
app.include_router(series.router, prefix="/api", tags=["Series"])
app.include_router(categories.router, prefix="/api", tags=["Categories"])  # 现在包含管理端API
app.include_router(videos.router, prefix="/api", tags=["Videos"])
app.include_router(payments.router, prefix="/api", tags=["Payments"])
app.include_router(users.router, prefix="/api", tags=["Users"])
app.include_router(share.router, prefix="/api", tags=["Share"])
app.include_router(analytics.router, prefix="/api", tags=["Analytics"])
app.include_router(search.router, prefix="/api", tags=["Search"])
app.include_router(playlist.router, prefix="/api", tags=["Playlist"])
app.include_router(products.router, prefix="/api", tags=["Products"])
app.include_router(cache.router, prefix="/api", tags=["Cache"])
app.include_router(admin_series.router, prefix="/api", tags=["Admin Series"])
app.include_router(admin_videos.router, prefix="/api", tags=["Admin Videos"])
app.include_router(admin_users.router, prefix="/api", tags=["Admin Users"])
app.include_router(user_data.router, prefix="/api", tags=["User Data"])
app.include_router(users_crud.router, prefix="/api", tags=["Users CRUD"])

@app.get("/", tags=["Root"])
def read_root():
    return {"message": "Welcome to the Shuimu Mock API!"}

# To run this server:
# 1. Make sure you have fastapi and uvicorn installed:
#    pip install fastapi "uvicorn[standard]"
# 2. Navigate to the `01-shuimu_01/mock_server` directory in your terminal.
# 3. Run the server:
#    uvicorn src.main:app --reload
# The server will be available at http://localhost:8000

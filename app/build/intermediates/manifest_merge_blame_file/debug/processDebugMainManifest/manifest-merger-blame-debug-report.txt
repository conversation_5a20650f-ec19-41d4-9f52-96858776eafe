1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.shuimu.course"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
14-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:5-92
14-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:22-89
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->[androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
15-->[androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:22-76
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
16-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
17
18    <permission
18-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
19        android:name="com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:10:5-45:19
25        android:name="com.shuimu.course.CourseApplication"
25-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:11:9-42
26        android:allowBackup="true"
26-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:12:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:13:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:14:9-54
32        android:label="@string/app_name"
32-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:15:9-41
33        android:networkSecurityConfig="@xml/network_security_config"
33-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:18:9-69
34        android:supportsRtl="true"
34-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:16:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.ShuimuCourse" >
36-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:17:9-50
37        <activity
37-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:20:9-29:20
38            android:name="com.shuimu.course.MainActivity"
38-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:21:13-41
39            android:exported="true"
39-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:22:13-36
40            android:theme="@style/Theme.ShuimuCourse" >
40-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:23:13-54
41            <intent-filter>
41-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:24:13-28:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:17-69
42-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:17-77
44-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:27-74
45            </intent-filter>
46        </activity>
47
48        <service
48-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:31:9-38:19
49            android:name="com.shuimu.course.player.PlaybackService"
49-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:32:13-51
50            android:exported="true"
50-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:33:13-36
51            android:foregroundServiceType="mediaPlayback" >
51-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:34:13-58
52            <intent-filter>
52-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:35:13-37:29
53                <action android:name="androidx.media3.session.MediaSessionService" />
53-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:17-85
53-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:25-83
54            </intent-filter>
55        </service>
56        <service
56-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
57            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
57-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
58            android:directBootAware="false"
58-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
59            android:enabled="@bool/enable_system_alarm_service_default"
59-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
60            android:exported="false" />
60-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
61        <service
61-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
62            android:name="androidx.work.impl.background.systemjob.SystemJobService"
62-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
63            android:directBootAware="false"
63-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
64            android:enabled="@bool/enable_system_job_service_default"
64-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
65            android:exported="true"
65-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
66            android:permission="android.permission.BIND_JOB_SERVICE" />
66-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
67        <service
67-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
68            android:name="androidx.work.impl.foreground.SystemForegroundService"
68-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
69            android:directBootAware="false"
69-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
70            android:enabled="@bool/enable_system_foreground_service_default"
70-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
71            android:exported="false" />
71-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
72
73        <receiver
73-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
74            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
74-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
75            android:directBootAware="false"
75-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
76            android:enabled="true"
76-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
77            android:exported="false" />
77-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
78        <receiver
78-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
79            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
79-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
80            android:directBootAware="false"
80-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
81            android:enabled="false"
81-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
82            android:exported="false" >
82-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
83            <intent-filter>
83-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
84                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
84-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
84-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
85                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
85-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
85-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
86            </intent-filter>
87        </receiver>
88        <receiver
88-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
89            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
89-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
90            android:directBootAware="false"
90-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
91            android:enabled="false"
91-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
92            android:exported="false" >
92-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
93            <intent-filter>
93-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
94                <action android:name="android.intent.action.BATTERY_OKAY" />
94-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
94-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
95                <action android:name="android.intent.action.BATTERY_LOW" />
95-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
95-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
96            </intent-filter>
97        </receiver>
98        <receiver
98-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
99            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
99-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
100            android:directBootAware="false"
100-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
101            android:enabled="false"
101-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
102            android:exported="false" >
102-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
103            <intent-filter>
103-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
104                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
104-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
104-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
105                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
105-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
105-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
106            </intent-filter>
107        </receiver>
108        <receiver
108-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
109            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
109-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
110            android:directBootAware="false"
110-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
111            android:enabled="false"
111-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
112            android:exported="false" >
112-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
113            <intent-filter>
113-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
114                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
114-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
114-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
115            </intent-filter>
116        </receiver>
117        <receiver
117-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
118            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
118-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
120            android:enabled="false"
120-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
121            android:exported="false" >
121-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
122            <intent-filter>
122-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
123                <action android:name="android.intent.action.BOOT_COMPLETED" />
123-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
123-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
124                <action android:name="android.intent.action.TIME_SET" />
124-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
124-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
125                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
125-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
125-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
126            </intent-filter>
127        </receiver>
128        <receiver
128-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
129            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
129-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
130            android:directBootAware="false"
130-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
131            android:enabled="@bool/enable_system_alarm_service_default"
131-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
132            android:exported="false" >
132-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
133            <intent-filter>
133-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
134                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
134-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
134-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
135            </intent-filter>
136        </receiver>
137        <receiver
137-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
138            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
138-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
140            android:enabled="true"
140-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
141            android:exported="true"
141-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
142            android:permission="android.permission.DUMP" >
142-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
143            <intent-filter>
143-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
144                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
144-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
144-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
145            </intent-filter>
146        </receiver>
147
148        <activity
148-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
149            android:name="androidx.compose.ui.tooling.PreviewActivity"
149-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
150            android:exported="true" />
150-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
151        <activity
151-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
152            android:name="androidx.activity.ComponentActivity"
152-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
153            android:exported="true" />
153-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
154
155        <service
155-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
156            android:name="androidx.room.MultiInstanceInvalidationService"
156-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
157            android:directBootAware="true"
157-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
158            android:exported="false" />
158-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
159
160        <receiver
160-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
161            android:name="androidx.profileinstaller.ProfileInstallReceiver"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
162            android:directBootAware="false"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
163            android:enabled="true"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
164            android:exported="true"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
165            android:permission="android.permission.DUMP" >
165-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
167                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
167-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
167-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
170                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
170-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
170-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
171            </intent-filter>
172            <intent-filter>
172-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
173                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
173-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
174            </intent-filter>
175            <intent-filter>
175-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
176                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
176-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
176-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
177            </intent-filter>
178        </receiver>
179    </application>
180
181</manifest>

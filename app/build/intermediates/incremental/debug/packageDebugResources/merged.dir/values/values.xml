<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">水幕视频课程App</string>
    <style name="Theme.ShuimuCourse" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryVariant">@color/black</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/black</item>
        <item name="colorSecondaryVariant">@color/black</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
    </style>
</resources>
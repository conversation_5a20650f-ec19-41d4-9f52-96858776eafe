package com.shuimu.course.data.remote.dto.user

import com.google.gson.annotations.SerializedName

/**
 * 用户相关的数据传输对象
 */

/**
 * 用户创建请求
 */
data class UserCreateRequestDto(
    val username: String,
    val email: String,
    val password: String,
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("is_admin")
    val isAdmin: Boolean = false
)

/**
 * 用户更新请求
 */
data class UserUpdateRequestDto(
    val username: String? = null,
    val email: String? = null,
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("avatar_url")
    val avatarUrl: String? = null,
    @SerializedName("is_active")
    val isActive: Boolean? = null,
    @SerializedName("is_admin")
    val isAdmin: Boolean? = null
)

/**
 * 用户资料更新请求
 */
data class UserProfileUpdateRequestDto(
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("avatar_url")
    val avatarUrl: String? = null
)

/**
 * 用户响应
 */
data class UserResponseDto(
    val success: Boolean,
    val data: UserDto,
    val message: String? = null
)

/**
 * 用户列表响应
 */
data class UserListResponseDto(
    val success: Boolean,
    val data: List<UserDto>,
    val pagination: PaginationDto,
    val message: String? = null
)

/**
 * 用户资料响应
 */
data class UserProfileResponseDto(
    val success: Boolean,
    val data: UserProfileDto,
    val message: String? = null
)

/**
 * 用户数据
 */
data class UserDto(
    val id: String,
    @SerializedName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("avatar_url")
    val avatarUrl: String?,
    val phone: String?,
    @SerializedName("is_active")
    val isActive: Boolean,
    @SerializedName("is_admin")
    val isAdmin: Boolean,
    @SerializedName("last_login_at")
    val lastLoginAt: String?,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("updated_at")
    val updatedAt: String?
)

/**
 * 用户资料数据
 */
data class UserProfileDto(
    val id: String,
    @SerializedName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("avatar_url")
    val avatarUrl: String?,
    val phone: String?,
    @SerializedName("is_active")
    val isActive: Boolean,
    @SerializedName("last_login_at")
    val lastLoginAt: String?,
    @SerializedName("created_at")
    val createdAt: String,
    val stats: UserStatsDto?
)

/**
 * 用户统计数据
 */
data class UserStatsDto(
    @SerializedName("watched_videos")
    val watchedVideos: Int,
    @SerializedName("purchased_series")
    val purchasedSeries: Int,
    @SerializedName("total_watch_count")
    val totalWatchCount: Int
)

/**
 * 分页数据
 */
data class PaginationDto(
    val page: Int,
    @SerializedName("page_size")
    val pageSize: Int,
    val total: Int,
    val pages: Int
)

/**
 * 通用响应
 */
data class ApiResponseDto<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val error: String? = null
)

/**
 * 错误响应
 */
data class ErrorResponseDto(
    val success: Boolean = false,
    val message: String,
    val error: String? = null,
    val code: Int? = null
)

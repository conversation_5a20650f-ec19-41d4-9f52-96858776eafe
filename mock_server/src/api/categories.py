#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类API - 使用MySQL数据库（修复版本）
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime
from datetime import datetime

router = APIRouter()

# 数据模型
class CategoryCreateRequest(BaseModel):
    title: str
    seriesId: str
    price: float = 0.0
    description: Optional[str] = None
    order_index: int = 0
    isFree: bool = False

class CategoryUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    order_index: Optional[int] = None

@router.get("/categories")
def get_all_categories(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    series_id: Optional[str] = Query(None)
):
    """获取分类列表"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 构建查询条件
        where_conditions = ["s.is_published = 1"]
        params = []

        if search:
            where_conditions.append("(c.title LIKE %s OR c.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if series_id:
            where_conditions.append("c.series_id = %s")
            params.append(series_id)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_query = f"""
            SELECT COUNT(*) as total
            FROM categories c
            LEFT JOIN series s ON c.series_id = s.id
            WHERE {where_clause}
        """
        total_result = mysql_manager.execute_query(count_query, tuple(params))
        total = total_result[0]['total'] if total_result else 0

        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f"""
            SELECT c.*,
                   s.title as series_title,
                   COUNT(v.id) as video_count
            FROM categories c
            LEFT JOIN series s ON c.series_id = s.id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE {where_clause}
            GROUP BY c.id
            ORDER BY c.series_id, c.order_index
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        categories_data = mysql_manager.execute_query(data_query, tuple(params))

        # 为每个分类添加视频列表
        for category in categories_data:
            videos = mysql_manager.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category['id'],))

            # 转换视频数据格式
            category['videos'] = []
            for video in videos:
                video_dict = {
                    'id': video['id'],
                    'title': video['title'],
                    'description': video['description'],
                    'duration': video['duration'],
                    'cloudUrl': video['video_url'],
                    'localPath': '',
                    'playCount': 0,
                    'categoryId': video['category_id']
                }
                category['videos'].append(video_dict)

            # 转换分类数据格式
            category['seriesId'] = category['series_id']
            category['price'] = float(category['price']) if category['price'] else 0.0
            category['isFree'] = category['price'] == 0
            category['isPurchased'] = False
            category['defaultExpanded'] = False
            category['totalVideos'] = category['video_count']
            category['progress'] = 0.0
            category['watchCount'] = 0

        return {
            'success': True,
            'data': categories_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'pages': (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

@router.get("/categories/{category_id}")
def get_category_by_id(category_id: str):
    """根据ID获取分类"""
    try:
        from ..database.mysql_manager import mysql_manager
        category = mysql_manager.get_category_by_id(category_id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        return category
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.post("/admin/categories")
def create_category(data: CategoryCreateRequest):
    """创建分类（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 生成新ID
        new_id = str(uuid.uuid4())
        
        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO categories (id, title, series_id, price, description, order_index, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            data.title,
            data.seriesId,
            data.price,
            data.description,
            data.order_index
        ))
        
        if affected_rows > 0:
            # 验证数据是否真的保存到数据库
            created_category = mysql_manager.get_category_by_id(new_id)

            if not created_category:
                print(f"❌ 分类创建验证失败: ID {new_id} 在数据库中不存在")
                raise HTTPException(status_code=500, detail="分类创建失败：数据未正确保存到数据库")

            print(f"✅ 分类创建成功并验证: {created_category['title']} (ID: {new_id})")

            return {
                "success": True,
                "message": "分类创建成功",
                "data": {
                    "id": new_id,
                    "title": data.title,
                    "seriesId": data.seriesId,
                    "price": data.price,
                    "description": data.description,
                    "order_index": data.order_index,
                    "isFree": data.isFree,
                    "videoIds": [],
                    "created_at": created_category.get('created_at', datetime.now().isoformat()),
                    "updated_at": created_category.get('updated_at', datetime.now().isoformat())
                }
            }
        else:
            print(f"❌ 分类创建失败: affected_rows = {affected_rows}")
            raise HTTPException(status_code=500, detail="创建分类失败：数据库操作未影响任何行")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")

@router.put("/admin/categories/{category_id}")
def update_category(category_id: str, data: CategoryUpdateRequest):
    """更新分类（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 检查分类是否存在
        existing_category = mysql_manager.get_category_by_id(category_id)
        if not existing_category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 更新分类
        update_data = data.dict(exclude_unset=True)
        success = mysql_manager.update_category(category_id, update_data)

        if success:
            return {
                "success": True,
                "message": "分类更新成功",
                "data": mysql_manager.get_category_by_id(category_id)
            }
        else:
            raise HTTPException(status_code=500, detail="更新分类失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新分类失败: {str(e)}")

@router.delete("/admin/categories/{category_id}")
def delete_category(category_id: str):
    """删除分类（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 检查分类是否存在
        existing_category = mysql_manager.get_category_by_id(category_id)
        if not existing_category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查是否有关联的视频
        videos = mysql_manager.execute_query("""
            SELECT COUNT(*) as count FROM videos WHERE category_id = %s
        """, (category_id,))
        
        if videos[0]['count'] > 0:
            return {
                "success": False,
                "message": f"无法删除分类，还有{videos[0]['count']}个关联视频"
            }
        
        # 删除分类
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM categories WHERE id = %s
        """, (category_id,))
        
        if affected_rows > 0:
            return {
                "success": True,
                "message": "分类删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除分类失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}")

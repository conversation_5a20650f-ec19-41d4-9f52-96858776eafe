{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ed624b17e998c266cfbf9ad645cdecae\\transformed\\jetified-material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4788,4879,4965,5072,5152,5237,5339,5451,5549,5649,5737,5853,5954,6057,6189,6269,6379", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4783,4874,4960,5067,5147,5232,5334,5446,5544,5644,5732,5848,5949,6052,6184,6264,6374,6472"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10378,10498,10616,10738,10859,10958,11052,11164,11308,11427,11574,11658,11758,11859,11960,12081,12208,12313,12463,12609,12739,12931,13057,13175,13298,13431,13533,13638,13762,13887,13989,14096,14201,14346,14498,14607,14716,14803,14896,14991,15111,15202,15288,15395,15475,15560,15662,15774,15872,15972,16060,16176,16277,16380,16512,16592,16702", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "10493,10611,10733,10854,10953,11047,11159,11303,11422,11569,11653,11753,11854,11955,12076,12203,12308,12458,12604,12734,12926,13052,13170,13293,13426,13528,13633,13757,13882,13984,14091,14196,14341,14493,14602,14711,14798,14891,14986,15106,15197,15283,15390,15470,15555,15657,15769,15867,15967,16055,16171,16272,16375,16507,16587,16697,16795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad952cb150bedcbfcb57f35849ccf9e8\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "269,270", "startColumns": "4,4", "startOffsets": "23445,23534", "endColumns": "88,94", "endOffsets": "23529,23624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\c12dcf7643131f54deb56aa398bbca93\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,265", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4237,4335,4437,4536,4638,4742,4846,23075", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "4330,4432,4531,4633,4737,4841,4959,23171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\50a9f4044b9bccbf0a8c519c38d7ae77\\transformed\\jetified-media3-ui-1.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3530,3596,3648,3708,3782,3856,3910", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53,65", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3525,3591,3643,3703,3777,3851,3905,3971"}, "to": {"startLines": "2,11,15,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,5964,6053,6144,6223,6321,6418,6497,6563,6669,6776,6841,6907,6971,7043,7163,7286,7408,7483,7571,7644,7724,7815,7908,7974,8751,8804,8864,8912,8973,9044,9115,9182,9260,9325,9384,9450,9515,9581,9633,9693,9767,9841,9895", "endLines": "10,14,18,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53,65", "endOffsets": "381,573,782,6048,6139,6218,6316,6413,6492,6558,6664,6771,6836,6902,6966,7038,7158,7281,7403,7478,7566,7639,7719,7810,7903,7969,8033,8799,8859,8907,8968,9039,9110,9177,9255,9320,9379,9445,9510,9576,9628,9688,9762,9836,9890,9956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60456bf525e41666fc62eb9c2882cd30\\transformed\\jetified-media3-session-1.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,132,214,282,351,436,513,611", "endColumns": "76,81,67,68,84,76,97,103", "endOffsets": "127,209,277,346,431,508,606,710"}, "to": {"startLines": "50,69,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3712,5564,17805,17873,17942,18027,18104,18202", "endColumns": "76,81,67,68,84,76,97,103", "endOffsets": "3784,5641,17868,17937,18022,18099,18197,18301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3789,3869,3950,4033,4142,4964,5062,5192,5833,5898,9961,10058,10312,16800,16902,16967,17042,17098,17177,17237,17291,17413,17472,17534,17588,17670,18306,18398,18473,18568,18649,18733,18877,18956,19037,19178,19271,19350,19405,19456,19522,19602,19683,19754,19834,19907,19985,20058,20130,20242,20335,20407,20499,20591,20665,20749,20841,20898,20982,21048,21131,21218,21280,21344,21407,21485,21587,21691,21788,21892,21951,22185,22527,22614,22767", "endLines": "22,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,3864,3945,4028,4137,4232,5057,5187,5272,5893,5959,10053,10136,10373,16897,16962,17037,17093,17172,17232,17286,17408,17467,17529,17583,17665,17800,18393,18468,18563,18644,18728,18872,18951,19032,19173,19266,19345,19400,19451,19517,19597,19678,19749,19829,19902,19980,20053,20125,20237,20330,20402,20494,20586,20660,20744,20836,20893,20977,21043,21126,21213,21275,21339,21402,21480,21582,21686,21783,21887,21946,22001,22269,22609,22686,22843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1081,1196,1306,1388,1494,1624,1702,1778,1869,1962,2060,2155,2255,2348,2441,2536,2627,2718,2804,2914,3025,3128,3239,3347,3454,3613,22440", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1076,1191,1301,1383,1489,1619,1697,1773,1864,1957,2055,2150,2250,2343,2436,2531,2622,2713,2799,2909,3020,3123,3234,3342,3449,3608,3707,22522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\98772e44dd82187f64bd43b721368829\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8038,8112,8177,8246,8318,8401,8478,8575,8668", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "8107,8172,8241,8313,8396,8473,8570,8663,8746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60558d3ccad013c87c458ddc43ef9e3a\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1010,1095,1171,1246,1324,1398,1476,1545", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,1005,1090,1166,1241,1319,1393,1471,1540,1662"}, "to": {"startLines": "66,67,68,70,71,128,129,252,253,255,256,260,262,263,264,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5277,5376,5464,5646,5746,10141,10220,22006,22098,22274,22355,22691,22848,22923,23001,23176,23254,23323", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "5371,5459,5559,5741,5828,10215,10307,22093,22180,22350,22435,22762,22918,22996,23070,23249,23318,23440"}}]}]}
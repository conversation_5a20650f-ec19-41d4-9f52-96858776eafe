#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Videos API - RESTful CRUD operations
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime

router = APIRouter()

# 数据模型
class VideoCreateRequest(BaseModel):
    title: str
    description: Optional[str] = ""
    category_id: str
    video_url: str
    duration: Optional[int] = 0
    order_index: Optional[int] = 0

class VideoUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[str] = None
    video_url: Optional[str] = None
    duration: Optional[int] = None
    order_index: Optional[int] = None

@router.get("/videos")
def get_all_videos(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[str] = Query(None),
    series_id: Optional[str] = Query(None)
):
    """获取视频列表"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 构建查询条件
        where_conditions = []
        params = []

        if search:
            where_conditions.append("(v.title LIKE %s OR v.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if category_id:
            where_conditions.append("v.category_id = %s")
            params.append(category_id)

        if series_id:
            where_conditions.append("c.series_id = %s")
            params.append(series_id)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 获取总数
        count_query = f"""
            SELECT COUNT(*) as total
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE s.is_published = 1 AND {where_clause}
        """
        total_result = mysql_manager.execute_query(count_query, tuple(params))
        total = total_result[0]['total'] if total_result else 0

        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f"""
            SELECT v.*,
                   c.title as category_title,
                   c.series_id,
                   s.title as series_title
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE s.is_published = 1 AND {where_clause}
            ORDER BY c.series_id, c.order_index, v.order_index
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        videos_data = mysql_manager.execute_query(data_query, tuple(params))

        # 转换数据格式
        for video in videos_data:
            video['categoryId'] = video['category_id']
            video['cloudUrl'] = video['video_url']
            video['localPath'] = ''
            video['playCount'] = 0

        return {
            'success': True,
            'data': videos_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'pages': (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")

@router.get("/videos/{video_id}")
def get_video_by_id(video_id: str):
    """根据ID获取视频"""
    try:
        from ..database.mysql_manager import mysql_manager
        video = mysql_manager.get_video_by_id(video_id)
        if not video:
            raise HTTPException(status_code=404, detail="视频不存在")

        # 转换数据格式
        video['categoryId'] = video['category_id']
        video['cloudUrl'] = video['video_url']
        video['localPath'] = ''
        video['playCount'] = 0

        return {
            'success': True,
            'data': video
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频失败: {str(e)}")

@router.post("/admin/videos")
def create_video(data: VideoCreateRequest):
    """创建视频（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 验证分类是否存在
        category = mysql_manager.get_category_by_id(data.category_id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")

        # 生成新ID
        new_id = str(uuid.uuid4())

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO videos (id, title, description, category_id, video_url, duration, order_index, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            data.title,
            data.description,
            data.category_id,
            data.video_url,
            data.duration,
            data.order_index
        ))

        if affected_rows > 0:
            # 验证数据是否真的保存到数据库
            created_video = mysql_manager.get_video_by_id(new_id)

            if not created_video:
                print(f"❌ 视频创建验证失败: ID {new_id} 在数据库中不存在")
                raise HTTPException(status_code=500, detail="视频创建失败：数据未正确保存到数据库")

            print(f"✅ 视频创建成功并验证: {created_video['title']} (ID: {new_id})")

            return {
                "success": True,
                "message": "视频创建成功",
                "data": {
                    "id": new_id,
                    "title": data.title,
                    "description": data.description,
                    "categoryId": data.category_id,
                    "cloudUrl": data.video_url,
                    "duration": data.duration,
                    "order_index": data.order_index,
                    "localPath": "",
                    "playCount": 0,
                    "created_at": created_video.get('created_at', datetime.now().isoformat()),
                    "updated_at": created_video.get('updated_at', datetime.now().isoformat())
                }
            }
        else:
            print(f"❌ 视频创建失败: affected_rows = {affected_rows}")
            raise HTTPException(status_code=500, detail="创建视频失败：数据库操作未影响任何行")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建视频失败: {str(e)}")

@router.put("/admin/videos/{video_id}")
def update_video(video_id: str, data: VideoUpdateRequest):
    """更新视频（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 检查视频是否存在
        existing_video = mysql_manager.get_video_by_id(video_id)
        if not existing_video:
            raise HTTPException(status_code=404, detail="视频不存在")

        # 如果更新分类，验证分类是否存在
        if data.category_id and data.category_id != existing_video['category_id']:
            category = mysql_manager.get_category_by_id(data.category_id)
            if not category:
                raise HTTPException(status_code=404, detail="目标分类不存在")

        # 更新视频
        update_data = data.dict(exclude_unset=True)
        success = mysql_manager.update_video(video_id, update_data)

        if success:
            updated_video = mysql_manager.get_video_by_id(video_id)
            return {
                "success": True,
                "message": "视频更新成功",
                "data": updated_video
            }
        else:
            raise HTTPException(status_code=500, detail="更新视频失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新视频失败: {str(e)}")

@router.delete("/admin/videos/{video_id}")
def delete_video(video_id: str):
    """删除视频（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 检查视频是否存在
        existing_video = mysql_manager.get_video_by_id(video_id)
        if not existing_video:
            raise HTTPException(status_code=404, detail="视频不存在")

        # 删除视频
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM videos WHERE id = %s
        """, (video_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "视频删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除视频失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除视频失败: {str(e)}")

# 层级关系查询端点
@router.get("/categories/{category_id}/videos")
def get_videos_by_category(category_id: str):
    """获取分类下的所有视频"""
    try:
        from ..database.mysql_manager import mysql_manager

        # 验证分类是否存在
        category = mysql_manager.get_category_by_id(category_id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")

        # 获取分类下的视频
        videos = mysql_manager.execute_query("""
            SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
        """, (category_id,))

        # 转换数据格式
        for video in videos:
            video['categoryId'] = video['category_id']
            video['cloudUrl'] = video['video_url']
            video['localPath'] = ''
            video['playCount'] = 0

        return {
            'success': True,
            'data': videos
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类视频失败: {str(e)}")

{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60456bf525e41666fc62eb9c2882cd30\\transformed\\jetified-media3-session-1.3.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,138,215,286,353,435,521,617", "endColumns": "82,76,70,66,81,85,95,101", "endOffsets": "133,210,281,348,430,516,612,714"}, "to": {"startLines": "53,72,204,205,206,207,208,209", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3861,5689,17513,17584,17651,17733,17819,17915", "endColumns": "82,76,70,66,81,85,95,101", "endOffsets": "3939,5761,17579,17646,17728,17814,17910,18012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\50a9f4044b9bccbf0a8c519c38d7ae77\\transformed\\jetified-media3-ui-1.3.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1939,2049,2158,2234,2321,2394,2465,2556,2648,2715,2780,2833,2891,2939,3000,3066,3130,3193,3258,3322,3383,3449,3514,3580,3632,3694,3770,3846,3902", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55,67", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1934,2044,2153,2229,2316,2389,2460,2551,2643,2710,2775,2828,2886,2934,2995,3061,3125,3188,3253,3317,3378,3444,3509,3575,3627,3689,3765,3841,3897,3965"}, "to": {"startLines": "2,11,16,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,6081,6162,6244,6326,6415,6506,6576,6642,6735,6829,6897,6961,7024,7096,7203,7313,7422,7498,7585,7658,7729,7820,7912,7979,8708,8761,8819,8867,8928,8994,9058,9121,9186,9250,9311,9377,9442,9508,9560,9622,9698,9774,9830", "endLines": "10,15,20,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55,67", "endOffsets": "377,646,907,6157,6239,6321,6410,6501,6571,6637,6730,6824,6892,6956,7019,7091,7198,7308,7417,7493,7580,7653,7724,7815,7907,7974,8039,8756,8814,8862,8923,8989,9053,9116,9181,9245,9306,9372,9437,9503,9555,9617,9693,9769,9825,9893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1121,1187,1279,1347,1410,1513,1573,1639,1695,1766,1826,1880,1992,2049,2110,2164,2240,2365,2451,2528,2621,2705,2788,2926,3007,3090,3221,3309,3387,3441,3497,3563,3637,3715,3786,3868,3943,4019,4094,4165,4272,4362,4435,4527,4623,4695,4771,4867,4920,5002,5069,5156,5243,5305,5369,5432,5501,5606,5716,5812,5920,5978,6038,6118,6201,6277", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "316,392,468,548,655,748,842,973,1054,1116,1182,1274,1342,1405,1508,1568,1634,1690,1761,1821,1875,1987,2044,2105,2159,2235,2360,2446,2523,2616,2700,2783,2921,3002,3085,3216,3304,3382,3436,3492,3558,3632,3710,3781,3863,3938,4014,4089,4160,4267,4357,4430,4522,4618,4690,4766,4862,4915,4997,5064,5151,5238,5300,5364,5427,5496,5601,5711,5807,5915,5973,6033,6113,6196,6272,6349"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,75,76,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,257,261,262,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3944,4020,4096,4176,4283,5102,5196,5327,5953,6015,9898,9990,10226,16558,16661,16721,16787,16843,16914,16974,17028,17140,17197,17258,17312,17388,18017,18103,18180,18273,18357,18440,18578,18659,18742,18873,18961,19039,19093,19149,19215,19289,19367,19438,19520,19595,19671,19746,19817,19924,20014,20087,20179,20275,20347,20423,20519,20572,20654,20721,20808,20895,20957,21021,21084,21153,21258,21368,21464,21572,21630,21867,22199,22282,22431", "endLines": "25,54,55,56,57,58,66,67,68,75,76,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,257,261,262,264", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "1128,4015,4091,4171,4278,4371,5191,5322,5403,6010,6076,9985,10053,10284,16656,16716,16782,16838,16909,16969,17023,17135,17192,17253,17307,17383,17508,18098,18175,18268,18352,18435,18573,18654,18737,18868,18956,19034,19088,19144,19210,19284,19362,19433,19515,19590,19666,19741,19812,19919,20009,20082,20174,20270,20342,20418,20514,20567,20649,20716,20803,20890,20952,21016,21079,21148,21253,21363,21459,21567,21625,21685,21942,22277,22353,22503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\c12dcf7643131f54deb56aa398bbca93\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "59,60,61,62,63,64,65,268", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4376,4474,4576,4673,4777,4881,4986,22739", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4469,4571,4668,4772,4876,4981,5097,22835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1240,1341,1447,1533,1637,1759,1843,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3378,3487,3594,3764,22112", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "1235,1336,1442,1528,1632,1754,1838,1919,2010,2103,2198,2292,2392,2485,2580,2685,2776,2867,2953,3058,3164,3267,3373,3482,3589,3759,3856,22194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60558d3ccad013c87c458ddc43ef9e3a\\transformed\\jetified-ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1311,1387,1466,1536", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1306,1382,1461,1531,1649"}, "to": {"startLines": "69,70,71,73,74,131,132,255,256,258,259,263,265,266,267,269,270,271", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5408,5505,5592,5766,5867,10058,10135,21690,21782,21947,22027,22358,22508,22585,22663,22840,22919,22989", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "5500,5587,5684,5862,5948,10130,10221,21777,21862,22022,22107,22426,22580,22658,22734,22914,22984,23102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ed624b17e998c266cfbf9ad645cdecae\\transformed\\jetified-material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10289,10405,10521,10648,10764,10862,10956,11067,11203,11322,11464,11549,11649,11744,11842,11958,12083,12188,12329,12469,12602,12782,12907,13027,13152,13274,13370,13468,13585,13715,13815,13917,14026,14168,14317,14426,14529,14606,14704,14802,14911,15000,15086,15193,15273,15356,15453,15556,15649,15747,15834,15942,16039,16141,16274,16354,16461", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "10400,10516,10643,10759,10857,10951,11062,11198,11317,11459,11544,11644,11739,11837,11953,12078,12183,12324,12464,12597,12777,12902,13022,13147,13269,13365,13463,13580,13710,13810,13912,14021,14163,14312,14421,14524,14601,14699,14797,14906,14995,15081,15188,15268,15351,15448,15551,15644,15742,15829,15937,16034,16136,16269,16349,16456,16553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad952cb150bedcbfcb57f35849ccf9e8\\transformed\\jetified-foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "23107,23195", "endColumns": "87,89", "endOffsets": "23190,23280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\98772e44dd82187f64bd43b721368829\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8044,8118,8179,8244,8315,8393,8465,8552,8635", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "8113,8174,8239,8310,8388,8460,8547,8630,8703"}}]}]}
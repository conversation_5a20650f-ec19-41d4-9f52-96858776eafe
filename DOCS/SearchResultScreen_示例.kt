package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.shuimu.course.presentation.ui.components.dialog.PaymentSlideUpSheet
import com.shuimu.course.presentation.ui.components.dialog.PaymentResultDialog
import com.shuimu.course.presentation.ui.components.dialog.PaymentResult
import com.shuimu.course.presentation.navigation.Routes
import com.shuimu.course.presentation.viewmodel.orders.PaymentViewModel
import com.shuimu.course.domain.model.PurchaseInfo
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

// 搜索结果数据模型示例
data class SearchResultItem(
    val id: String,
    val title: String,
    val price: String,
    val isPurchased: Boolean,
    val isFree: Boolean,
    val type: String, // "series", "category", "video"
    val description: String,
    val seriesTitle: String? = null // 系列标题，用于构建支付框标题
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchResultScreen(
    navController: NavController,
    searchQuery: String = "",
    paymentViewModel: PaymentViewModel = hiltViewModel()
) {
    // 支付弹窗状态
    var purchaseInfo by remember { mutableStateOf<PurchaseInfo?>(null) }
    val paymentState by paymentViewModel.state.collectAsState()
    
    // 模拟搜索结果数据
    val searchResults = remember {
        listOf(
            SearchResultItem(
                id = "series_1",
                title = "道：恋爱宝典系列",
                price = "600",
                isPurchased = false,
                isFree = false,
                type = "series",
                description = "全面的恋爱技巧指导"
            ),
            SearchResultItem(
                id = "category_1", 
                title = "基础心理学",
                price = "200",
                isPurchased = true,
                isFree = false,
                type = "category",
                description = "已购买的心理学基础课程"
            ),
            SearchResultItem(
                id = "series_2",
                title = "免费体验课程",
                price = "0",
                isPurchased = false,
                isFree = true,
                type = "series",
                description = "免费的入门课程"
            )
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("搜索结果: \"$searchQuery\"") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = androidx.compose.material.icons.Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(searchResults) { item ->
                    SearchResultItemCard(
                        item = item,
                        onClick = {
                            // 点击未购买项目时触发支付弹窗
                            if (!item.isPurchased && !item.isFree) {
                                val displayName = when (item.type) {
                                    "series" -> item.title
                                    "category", "video" -> {
                                        if (item.seriesTitle != null) {
                                            "${item.seriesTitle}·${item.title}"
                                        } else {
                                            item.title
                                        }
                                    }
                                    else -> item.title
                                }
                                
                                purchaseInfo = PurchaseInfo(
                                    id = item.id,
                                    name = displayName,
                                    price = "¥${item.price}",
                                    type = item.type
                                )
                            } else {
                                // 已购买或免费的，直接进入
                                when (item.type) {
                                    "series" -> navController.navigate("${Routes.COURSE_DETAIL}/${item.id}")
                                    "category" -> navController.navigate("${Routes.CATEGORY_DETAIL}/${item.id}")
                                    "video" -> navController.navigate("${Routes.VIDEO_PLAYER}/${item.id}")
                                }
                            }
                        }
                    )
                }
            }

            // 复用相同的支付弹窗组件
            purchaseInfo?.let { info ->
                PaymentSlideUpSheet(
                    visible = true,
                    courseTitle = info.name,
                    price = info.price.removePrefix("¥"),
                    onPay = {
                        paymentViewModel.processPayment(
                            itemId = info.id,
                            itemType = info.type,
                            amount = info.price.removePrefix("¥").toFloatOrNull() ?: 0f
                        )
                    },
                    onDismiss = { 
                        if (!paymentState.isLoading) {
                            purchaseInfo = null 
                        }
                    },
                    loading = paymentState.isLoading,
                    enableDragToClose = !paymentState.isLoading,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }

            // 支付结果弹窗
            val paymentResult = when {
                paymentState.paymentResult != null -> PaymentResult(
                    success = true,
                    orderNumber = "SM${System.currentTimeMillis()}",
                    courseTitle = purchaseInfo?.name ?: "课程",
                    amount = purchaseInfo?.price?.removePrefix("¥") ?: "0"
                )
                paymentState.error != null -> PaymentResult(
                    success = false,
                    message = paymentState.error
                )
                else -> null
            }

            paymentResult?.let { result ->
                PaymentResultDialog(
                    result = result,
                    onDismiss = { 
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                    },
                    onViewOrders = { 
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                        navController.navigate(Routes.ORDERS)
                    },
                    onStartStudy = { 
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                        // 导航到对应的学习页面
                        when (purchaseInfo?.type) {
                            "series" -> navController.navigate("${Routes.COURSE_DETAIL}/${purchaseInfo?.id}")
                            "category" -> navController.navigate("${Routes.CATEGORY_DETAIL}/${purchaseInfo?.id}")
                            "video" -> navController.navigate("${Routes.VIDEO_PLAYER}/${purchaseInfo?.id}")
                        }
                    },
                    onRetry = { 
                        paymentViewModel.clearPaymentState()
                        // 重新显示支付弹窗
                    }
                )
            }
        }
    }
}

@Composable
fun SearchResultItemCard(
    item: SearchResultItem,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.weight(1f)
                )
                
                when {
                    item.isFree -> {
                        Text(
                            text = "免费",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    item.isPurchased -> {
                        Text(
                            text = "已购买",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.secondary
                        )
                    }
                    else -> {
                        Text(
                            text = "¥${item.price}",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = item.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "类型: ${
                    when (item.type) {
                        "series" -> "系列课程"
                        "category" -> "分类课程"
                        "video" -> "单个视频"
                        else -> item.type
                    }
                }",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

@Preview
@Composable
fun SearchResultScreenPreview() {
    ShuimuCourseTheme {
        val navController = rememberNavController()
        SearchResultScreen(
            navController = navController,
            searchQuery = "恋爱"
        )
    }
} 
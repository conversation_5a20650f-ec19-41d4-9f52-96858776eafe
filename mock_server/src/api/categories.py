#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类API - 使用MySQL数据库（修复版本）
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime
from datetime import datetime

router = APIRouter()

# 数据模型
class CategoryCreateRequest(BaseModel):
    title: str
    seriesId: str
    price: float = 0.0
    description: Optional[str] = None
    order_index: int = 0
    isFree: bool = False

class CategoryUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    order_index: Optional[int] = None

@router.get("/categories")
def get_all_categories():
    """获取所有分类"""
    try:
        from ..database.mysql_manager import mysql_manager
        categories_data = mysql_manager.get_all_categories()
        return categories_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.get("/categories/{category_id}")
def get_category_by_id(category_id: str):
    """根据ID获取分类"""
    try:
        from ..database.mysql_manager import mysql_manager
        category = mysql_manager.get_category_by_id(category_id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        return category
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.post("/admin/categories")
def create_category(data: CategoryCreateRequest):
    """创建分类（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 生成新ID
        new_id = str(uuid.uuid4())
        
        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO categories (id, title, series_id, price, description, order_index, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            data.title,
            data.seriesId,
            data.price,
            data.description,
            data.order_index
        ))
        
        if affected_rows > 0:
            # 验证数据是否真的保存到数据库
            created_category = mysql_manager.get_category_by_id(new_id)

            if not created_category:
                print(f"❌ 分类创建验证失败: ID {new_id} 在数据库中不存在")
                raise HTTPException(status_code=500, detail="分类创建失败：数据未正确保存到数据库")

            print(f"✅ 分类创建成功并验证: {created_category['title']} (ID: {new_id})")

            return {
                "success": True,
                "message": "分类创建成功",
                "data": {
                    "id": new_id,
                    "title": data.title,
                    "seriesId": data.seriesId,
                    "price": data.price,
                    "description": data.description,
                    "order_index": data.order_index,
                    "isFree": data.isFree,
                    "videoIds": [],
                    "created_at": created_category.get('created_at', datetime.now().isoformat()),
                    "updated_at": created_category.get('updated_at', datetime.now().isoformat())
                }
            }
        else:
            print(f"❌ 分类创建失败: affected_rows = {affected_rows}")
            raise HTTPException(status_code=500, detail="创建分类失败：数据库操作未影响任何行")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")

@router.put("/categories/{category_id}")
def update_category(category_id: str, data: CategoryUpdateRequest):
    """更新分类"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 检查分类是否存在
        existing_category = mysql_manager.get_category_by_id(category_id)
        if not existing_category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 更新分类
        update_data = data.dict(exclude_unset=True)
        success = mysql_manager.update_category(category_id, update_data)

        if success:
            return {
                "success": True,
                "message": "分类更新成功",
                "data": mysql_manager.get_category_by_id(category_id)
            }
        else:
            raise HTTPException(status_code=500, detail="更新分类失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新分类失败: {str(e)}")

@router.delete("/admin/categories/{category_id}")
def delete_category(category_id: str):
    """删除分类（管理端）"""
    try:
        from ..database.mysql_manager import mysql_manager
        
        # 检查分类是否存在
        existing_category = mysql_manager.get_category_by_id(category_id)
        if not existing_category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查是否有关联的视频
        videos = mysql_manager.execute_query("""
            SELECT COUNT(*) as count FROM videos WHERE category_id = %s
        """, (category_id,))
        
        if videos[0]['count'] > 0:
            return {
                "success": False,
                "message": f"无法删除分类，还有{videos[0]['count']}个关联视频"
            }
        
        # 删除分类
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM categories WHERE id = %s
        """, (category_id,))
        
        if affected_rows > 0:
            return {
                "success": True,
                "message": "分类删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除分类失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
废弃端点处理
处理不符合RESTful原则的旧端点，提供迁移指导
"""

from fastapi import APIRouter, HTTPException, Header, Body
from typing import Optional, Dict, Any
from datetime import datetime

router = APIRouter()

# 废弃信息常量
DEPRECATION_INFO = {
    "deprecation_date": "2025-06-30",
    "removal_date": "2025-09-30",
    "migration_guide": "https://api.shuimu.us.kg/docs/migration"
}

def create_deprecation_response(old_endpoint: str, new_endpoint: str, additional_info: str = "") -> Dict[str, Any]:
    """创建废弃端点响应"""
    return {
        "success": False,
        "deprecated": True,
        "message": f"端点已废弃：{old_endpoint}",
        "replacement": new_endpoint,
        "deprecation_date": DEPRECATION_INFO["deprecation_date"],
        "removal_date": DEPRECATION_INFO["removal_date"],
        "migration_guide": DEPRECATION_INFO["migration_guide"],
        "additional_info": additional_info,
        "timestamp": datetime.now().isoformat()
    }

# ========== 废弃的视频相关端点 ==========

@router.put("/videos/{video_id}/progress", deprecated=True)
def update_video_progress_deprecated(
    video_id: str,
    position: int = Body(...),
    x_user_id: Optional[str] = Header("user_001")
):
    """
    废弃端点：更新视频播放进度
    
    ⚠️ 此端点已废弃，请使用用户数据端点：
    PUT /api/users/{user_id}/progress/{video_id}
    
    废弃原因：
    - 播放进度是用户个人数据，不是视频属性
    - 不符合RESTful设计原则
    - 权限控制不清晰
    
    迁移指南：
    1. 获取当前用户ID
    2. 使用新端点：PUT /api/users/{user_id}/progress/{video_id}
    3. 传递完整的进度数据对象而非单一position值
    """
    new_endpoint = f"/api/users/{x_user_id}/progress/{video_id}"
    additional_info = (
        "迁移步骤：\n"
        "1. 获取当前用户ID\n"
        "2. 构建进度数据对象：{\"position\": position, \"progress\": progress, \"lastWatchedAt\": timestamp}\n"
        "3. 调用新端点：PUT /api/users/{user_id}/progress/{video_id}\n"
        "4. 添加认证头：X-User-Id 和 X-Is-Admin"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/videos/{video_id}/progress",
        new_endpoint=f"PUT {new_endpoint}",
        additional_info=additional_info
    )

@router.put("/videos/{video_id}/watch-count", deprecated=True)
def update_watch_count_deprecated(
    video_id: str,
    x_user_id: Optional[str] = Header("user_001")
):
    """
    废弃端点：更新视频观看次数
    
    ⚠️ 此端点已废弃，请使用用户数据端点：
    PUT /api/users/{user_id}/progress/{video_id}
    
    废弃原因：
    - 观看次数是用户个人统计数据，不是视频属性
    - 不符合RESTful设计原则
    - 应该作为用户进度数据的一部分
    """
    new_endpoint = f"/api/users/{x_user_id}/progress/{video_id}"
    additional_info = (
        "迁移步骤：\n"
        "1. 获取当前用户ID\n"
        "2. 先获取当前进度：GET /api/users/{user_id}/progress/{video_id}\n"
        "3. 增加观看次数：watchCount + 1\n"
        "4. 更新进度数据：PUT /api/users/{user_id}/progress/{video_id}\n"
        "5. 传递完整的进度对象，包含更新后的watchCount"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/videos/{video_id}/watch-count",
        new_endpoint=f"PUT {new_endpoint}",
        additional_info=additional_info
    )

@router.put("/videos/{video_id}/cache-status", deprecated=True)
def update_video_cache_deprecated(
    video_id: str,
    is_cached: bool = Body(...),
    local_path: Optional[str] = Body(None),
    x_user_id: Optional[str] = Header("user_001")
):
    """
    废弃端点：更新视频缓存状态
    
    ⚠️ 此端点已废弃，请使用用户数据端点：
    PUT /api/users/{user_id}/cache/{video_id}
    
    废弃原因：
    - 缓存状态是用户个人数据，不是视频属性
    - 不符合RESTful设计原则
    - 每个用户的缓存状态可能不同
    """
    new_endpoint = f"/api/users/{x_user_id}/cache/{video_id}"
    additional_info = (
        "迁移步骤：\n"
        "1. 获取当前用户ID\n"
        "2. 构建缓存数据对象：{\"isCached\": true/false, \"localPath\": \"path\", \"cachedAt\": timestamp}\n"
        "3. 调用新端点：PUT /api/users/{user_id}/cache/{video_id}\n"
        "4. 添加认证头：X-User-Id 和 X-Is-Admin"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/videos/{video_id}/cache-status",
        new_endpoint=f"PUT {new_endpoint}",
        additional_info=additional_info
    )

@router.put("/videos/{video_id}/cache", deprecated=True)
def update_video_cache_alt_deprecated(
    video_id: str,
    is_cached: bool = Body(...),
    local_path: Optional[str] = Body(None),
    x_user_id: Optional[str] = Header("user_001")
):
    """
    废弃端点：更新视频缓存状态（备用端点）
    
    ⚠️ 此端点已废弃，请使用用户数据端点：
    PUT /api/users/{user_id}/cache/{video_id}
    """
    return update_video_cache_deprecated(video_id, is_cached, local_path, x_user_id)

# ========== 废弃的用户相关端点 ==========

@router.put("/user/settings", deprecated=True)
def update_user_settings_deprecated():
    """
    废弃端点：更新用户设置
    
    ⚠️ 此端点已废弃，请使用标准用户数据端点：
    PUT /api/users/{user_id}/settings
    """
    additional_info = (
        "迁移步骤：\n"
        "1. 获取当前用户ID\n"
        "2. 使用新端点：PUT /api/users/{user_id}/settings\n"
        "3. 添加认证头：X-User-Id 和 X-Is-Admin"
    )
    
    return create_deprecation_response(
        old_endpoint="PUT /api/user/settings",
        new_endpoint="PUT /api/users/{user_id}/settings",
        additional_info=additional_info
    )

# ========== 废弃的管理端端点 ==========

@router.put("/admin/users/{user_id}/progress/{video_id}", deprecated=True)
def update_admin_user_progress_deprecated(user_id: str, video_id: str):
    """
    废弃端点：管理端更新用户进度
    
    ⚠️ 此端点已废弃，请使用统一的用户数据端点：
    PUT /api/users/{user_id}/progress/{video_id}
    
    废弃原因：
    - 与App端端点功能重复
    - 增加了维护成本
    - 统一端点可以通过权限控制区分操作者
    """
    additional_info = (
        "迁移步骤：\n"
        "1. 使用统一端点：PUT /api/users/{user_id}/progress/{video_id}\n"
        "2. 设置管理员认证头：X-User-Id=admin_id, X-Is-Admin=true\n"
        "3. 权限验证会自动识别管理员身份\n"
        "4. 数据格式保持不变"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/admin/users/{user_id}/progress/{video_id}",
        new_endpoint=f"PUT /api/users/{user_id}/progress/{video_id}",
        additional_info=additional_info
    )

@router.put("/admin/users/{user_id}/cache/{video_id}", deprecated=True)
def update_admin_user_cache_deprecated(user_id: str, video_id: str):
    """
    废弃端点：管理端更新用户缓存
    
    ⚠️ 此端点已废弃，请使用统一的用户数据端点：
    PUT /api/users/{user_id}/cache/{video_id}
    """
    additional_info = (
        "迁移步骤：\n"
        "1. 使用统一端点：PUT /api/users/{user_id}/cache/{video_id}\n"
        "2. 设置管理员认证头：X-User-Id=admin_id, X-Is-Admin=true\n"
        "3. 权限验证会自动识别管理员身份"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/admin/users/{user_id}/cache/{video_id}",
        new_endpoint=f"PUT /api/users/{user_id}/cache/{video_id}",
        additional_info=additional_info
    )

@router.put("/admin/users/{user_id}/settings", deprecated=True)
def update_admin_user_settings_deprecated(user_id: str):
    """
    废弃端点：管理端更新用户设置
    
    ⚠️ 此端点已废弃，请使用统一的用户数据端点：
    PUT /api/users/{user_id}/settings
    """
    additional_info = (
        "迁移步骤：\n"
        "1. 使用统一端点：PUT /api/users/{user_id}/settings\n"
        "2. 设置管理员认证头：X-User-Id=admin_id, X-Is-Admin=true"
    )
    
    return create_deprecation_response(
        old_endpoint=f"PUT /api/admin/users/{user_id}/settings",
        new_endpoint=f"PUT /api/users/{user_id}/settings",
        additional_info=additional_info
    )

# ========== 获取废弃端点列表 ==========

@router.get("/deprecated/endpoints")
def get_deprecated_endpoints():
    """获取所有废弃端点列表"""
    deprecated_endpoints = [
        {
            "old_endpoint": "PUT /api/videos/{video_id}/progress",
            "new_endpoint": "PUT /api/users/{user_id}/progress/{video_id}",
            "reason": "播放进度是用户数据，不是视频属性",
            "status": "deprecated"
        },
        {
            "old_endpoint": "PUT /api/videos/{video_id}/watch-count",
            "new_endpoint": "PUT /api/users/{user_id}/progress/{video_id}",
            "reason": "观看次数是用户统计数据",
            "status": "deprecated"
        },
        {
            "old_endpoint": "PUT /api/videos/{video_id}/cache-status",
            "new_endpoint": "PUT /api/users/{user_id}/cache/{video_id}",
            "reason": "缓存状态是用户数据，不是视频属性",
            "status": "deprecated"
        },
        {
            "old_endpoint": "PUT /api/admin/users/{user_id}/progress/{video_id}",
            "new_endpoint": "PUT /api/users/{user_id}/progress/{video_id}",
            "reason": "统一端点，通过权限控制区分操作者",
            "status": "deprecated"
        },
        {
            "old_endpoint": "PUT /api/admin/users/{user_id}/cache/{video_id}",
            "new_endpoint": "PUT /api/users/{user_id}/cache/{video_id}",
            "reason": "统一端点，通过权限控制区分操作者",
            "status": "deprecated"
        }
    ]
    
    return {
        "success": True,
        "data": deprecated_endpoints,
        "deprecation_info": DEPRECATION_INFO,
        "total": len(deprecated_endpoints)
    }

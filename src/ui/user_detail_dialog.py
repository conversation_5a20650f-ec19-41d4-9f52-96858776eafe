#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any
import threading
from datetime import datetime

class UserDetailDialog:
    """用户详情对话框"""
    
    def __init__(self, parent, user_data: Dict[str, Any], api_client):
        self.parent = parent
        self.user_data = user_data
        self.api_client = api_client
        self.dialog = None
        self.user_progress = []
        self.user_stats = {}
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"用户详情 - {self.user_data.get('username', '未知用户')}")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        
        # 设置模态
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 加载用户详细数据
        self.load_user_details()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"800x600+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建Notebook
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息标签页
        self.create_basic_info_tab(notebook)
        
        # 学习统计标签页
        self.create_stats_tab(notebook)
        
        # 学习进度标签页
        self.create_progress_tab(notebook)
        
        # 操作历史标签页
        self.create_history_tab(notebook)
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="编辑用户", command=self.edit_user).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置密码", command=self.reset_password).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新数据", command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.dialog.destroy).pack(side=tk.RIGHT, padx=5)
    
    def create_basic_info_tab(self, notebook):
        """创建基本信息标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="基本信息")
        
        # 创建滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 用户信息字段
        info_fields = [
            ("用户ID", "id"),
            ("用户名", "username"),
            ("邮箱", "email"),
            ("显示名", "display_name"),
            ("手机号", "phone"),
            ("头像URL", "avatar_url"),
            ("账户状态", "is_active"),
            ("用户角色", "is_admin"),
            ("最后登录", "last_login_at"),
            ("创建时间", "created_at"),
            ("更新时间", "updated_at")
        ]
        
        self.info_vars = {}
        
        for i, (label, key) in enumerate(info_fields):
            # 标签
            ttk.Label(scrollable_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                row=i, column=0, sticky=tk.W, padx=10, pady=5
            )
            
            # 值
            value = self.user_data.get(key, "")
            if key == "is_active":
                value = "活跃" if value else "禁用"
            elif key == "is_admin":
                value = "管理员" if value else "普通用户"
            elif key in ["last_login_at", "created_at", "updated_at"]:
                value = self.format_datetime(value)
            
            var = tk.StringVar(value=str(value))
            self.info_vars[key] = var
            
            entry = ttk.Entry(scrollable_frame, textvariable=var, state="readonly", width=50)
            entry.grid(row=i, column=1, sticky=tk.W+tk.E, padx=10, pady=5)
        
        # 配置列权重
        scrollable_frame.columnconfigure(1, weight=1)
        
        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_stats_tab(self, notebook):
        """创建学习统计标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="学习统计")
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(frame, text="学习统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 统计字段
        self.stats_vars = {
            "watched_videos": tk.StringVar(value="加载中..."),
            "purchased_series": tk.StringVar(value="加载中..."),
            "total_watch_count": tk.StringVar(value="加载中..."),
            "total_watch_time": tk.StringVar(value="加载中..."),
            "completion_rate": tk.StringVar(value="加载中...")
        }
        
        stats_labels = [
            ("观看视频数", "watched_videos"),
            ("购买系列数", "purchased_series"),
            ("总观看次数", "total_watch_count"),
            ("总观看时长", "total_watch_time"),
            ("完成率", "completion_rate")
        ]
        
        for i, (label, key) in enumerate(stats_labels):
            ttk.Label(stats_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                row=i, column=0, sticky=tk.W, padx=10, pady=5
            )
            ttk.Label(stats_frame, textvariable=self.stats_vars[key]).grid(
                row=i, column=1, sticky=tk.W, padx=10, pady=5
            )
        
        # 最近活动框架
        activity_frame = ttk.LabelFrame(frame, text="最近活动", padding=10)
        activity_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 最近活动列表
        columns = ("时间", "活动", "详情")
        self.activity_tree = ttk.Treeview(activity_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.activity_tree.heading(col, text=col)
            self.activity_tree.column(col, width=150)
        
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient=tk.VERTICAL, command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_progress_tab(self, notebook):
        """创建学习进度标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="学习进度")
        
        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(toolbar, text="筛选:").pack(side=tk.LEFT, padx=(0, 5))
        self.progress_filter_var = tk.StringVar(value="all")
        filter_combo = ttk.Combobox(toolbar, textvariable=self.progress_filter_var,
                                   values=["all", "completed", "in_progress", "not_started"],
                                   state="readonly", width=15)
        filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        filter_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_progress())
        
        ttk.Button(toolbar, text="刷新进度", command=self.load_user_progress).pack(side=tk.RIGHT)
        
        # 进度列表
        columns = ("视频", "系列", "分类", "进度", "观看次数", "最后观看", "状态")
        self.progress_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        column_widths = {
            "视频": 200,
            "系列": 150,
            "分类": 150,
            "进度": 80,
            "观看次数": 80,
            "最后观看": 150,
            "状态": 80
        }
        
        for col in columns:
            self.progress_tree.heading(col, text=col)
            self.progress_tree.column(col, width=column_widths.get(col, 100))
        
        progress_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.progress_tree.yview)
        self.progress_tree.configure(yscrollcommand=progress_scrollbar.set)
        
        self.progress_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)
        progress_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def create_history_tab(self, notebook):
        """创建操作历史标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="操作历史")
        
        # 历史记录列表
        columns = ("时间", "操作", "IP地址", "用户代理", "结果")
        self.history_tree = ttk.Treeview(frame, columns=columns, show="headings", height=20)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=150)
        
        history_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # 加载历史记录的占位符
        self.history_tree.insert("", tk.END, values=("暂无数据", "", "", "", ""))
    
    def load_user_details(self):
        """加载用户详细数据"""
        self.load_user_stats()
        self.load_user_progress()
    
    def load_user_stats(self):
        """加载用户统计数据"""
        def load_in_thread():
            try:
                user_id = self.user_data.get('id')
                if not user_id:
                    return
                
                # 获取用户资料（包含统计信息）
                response = self.api_client.get_user_profile(user_id)
                
                if response.get('success'):
                    user_profile = response.get('data', {})
                    stats = user_profile.get('stats', {})
                    
                    # 更新统计信息
                    self.after_update(lambda: self.update_stats_display(stats))
                else:
                    self.after_update(lambda: self.update_stats_error())
                    
            except Exception as e:
                self.after_update(lambda: self.update_stats_error())
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def load_user_progress(self):
        """加载用户学习进度"""
        def load_in_thread():
            try:
                user_id = self.user_data.get('id')
                if not user_id:
                    return
                
                # 获取用户所有进度
                response = self.api_client.get_user_progress(user_id)
                
                if response.get('success'):
                    self.user_progress = response.get('data', [])
                    self.after_update(self.update_progress_display)
                else:
                    self.after_update(lambda: self.update_progress_error())
                    
            except Exception as e:
                self.after_update(lambda: self.update_progress_error())
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def after_update(self, func):
        """在主线程中执行更新"""
        if self.dialog and self.dialog.winfo_exists():
            self.dialog.after(0, func)
    
    def update_stats_display(self, stats):
        """更新统计信息显示"""
        self.stats_vars["watched_videos"].set(str(stats.get("watched_videos", 0)))
        self.stats_vars["purchased_series"].set(str(stats.get("purchased_series", 0)))
        self.stats_vars["total_watch_count"].set(str(stats.get("total_watch_count", 0)))
        
        # 计算总观看时长（假设每次观看平均30分钟）
        total_minutes = stats.get("total_watch_count", 0) * 30
        hours = total_minutes // 60
        minutes = total_minutes % 60
        self.stats_vars["total_watch_time"].set(f"{hours}小时{minutes}分钟")
        
        # 计算完成率
        watched = stats.get("watched_videos", 0)
        if watched > 0:
            # 这里需要获取总视频数来计算完成率，暂时使用假数据
            completion_rate = min(100, (watched / 100) * 100)
            self.stats_vars["completion_rate"].set(f"{completion_rate:.1f}%")
        else:
            self.stats_vars["completion_rate"].set("0%")
    
    def update_stats_error(self):
        """更新统计信息错误状态"""
        for var in self.stats_vars.values():
            var.set("加载失败")
    
    def update_progress_display(self):
        """更新进度显示"""
        # 清空现有数据
        for item in self.progress_tree.get_children():
            self.progress_tree.delete(item)
        
        # 添加进度数据
        for progress in self.user_progress:
            progress_percent = f"{progress.get('progress', 0) * 100:.1f}%"
            status = "已完成" if progress.get('is_completed') else "进行中"
            
            values = (
                progress.get('video_title', '未知视频'),
                progress.get('series_title', '未知系列'),
                progress.get('category_title', '未知分类'),
                progress_percent,
                str(progress.get('watch_count', 0)),
                self.format_datetime(progress.get('last_watched_at')),
                status
            )
            
            self.progress_tree.insert("", tk.END, values=values)
    
    def update_progress_error(self):
        """更新进度错误状态"""
        for item in self.progress_tree.get_children():
            self.progress_tree.delete(item)
        self.progress_tree.insert("", tk.END, values=("加载失败", "", "", "", "", "", ""))
    
    def filter_progress(self):
        """筛选进度"""
        filter_value = self.progress_filter_var.get()
        
        # 清空现有显示
        for item in self.progress_tree.get_children():
            self.progress_tree.delete(item)
        
        # 根据筛选条件显示数据
        for progress in self.user_progress:
            show_item = True
            
            if filter_value == "completed" and not progress.get('is_completed'):
                show_item = False
            elif filter_value == "in_progress" and (progress.get('is_completed') or progress.get('progress', 0) == 0):
                show_item = False
            elif filter_value == "not_started" and progress.get('progress', 0) > 0:
                show_item = False
            
            if show_item:
                progress_percent = f"{progress.get('progress', 0) * 100:.1f}%"
                status = "已完成" if progress.get('is_completed') else "进行中"
                
                values = (
                    progress.get('video_title', '未知视频'),
                    progress.get('series_title', '未知系列'),
                    progress.get('category_title', '未知分类'),
                    progress_percent,
                    str(progress.get('watch_count', 0)),
                    self.format_datetime(progress.get('last_watched_at')),
                    status
                )
                
                self.progress_tree.insert("", tk.END, values=values)
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return "从未"
        try:
            if 'T' in dt_str:
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M")
            return dt_str
        except:
            return dt_str
    
    def edit_user(self):
        """编辑用户"""
        from .user_edit_dialog import UserEditDialog
        dialog = UserEditDialog(self.dialog, self.user_data, self.api_client)
        if dialog.show():
            # 刷新用户数据
            self.refresh_data()
    
    def reset_password(self):
        """重置密码"""
        if messagebox.askyesno("确认", f"确定要重置用户 '{self.user_data.get('username')}' 的密码吗？"):
            # TODO: 实现密码重置功能
            messagebox.showinfo("提示", "密码重置功能待实现")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_user_details()

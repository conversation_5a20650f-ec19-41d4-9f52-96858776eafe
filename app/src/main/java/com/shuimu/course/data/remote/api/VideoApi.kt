package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.VideoDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.PUT
import retrofit2.http.Path

interface VideoApi {
    @GET("categories/{id}/videos")
    suspend fun getVideosForCategory(@Path("id") categoryId: String): Response<List<VideoDto>>

    @GET("videos/{id}")
    suspend fun getVideoDetails(@Path("id") videoId: String): Response<VideoDto>

    // ========== 废弃的方法（不符合RESTful原则） ==========

    /**
     * @deprecated 此方法不符合RESTful原则，因为进度是用户数据而非视频数据
     * 请使用 UserDataApi.updateUserVideoProgress() 替代
     */
    @Deprecated(
        message = "使用 UserDataApi.updateUserVideoProgress() 替代",
        replaceWith = ReplaceWith("userDataApi.updateUserVideoProgress(userId, videoId, progressRequest)")
    )
    @PUT("videos/{id}/progress")
    suspend fun updateVideoProgress(@Path("id") videoId: String, @Body progress: Int): Response<Unit>

    /**
     * @deprecated 此方法不符合RESTful原则，因为观看次数是用户数据而非视频数据
     * 请使用 UserDataApi.updateUserVideoProgress() 替代
     */
    @Deprecated(
        message = "使用 UserDataApi.updateUserVideoProgress() 替代",
        replaceWith = ReplaceWith("userDataApi.updateUserVideoProgress(userId, videoId, progressRequest)")
    )
    @PUT("videos/{id}/watch-count")
    suspend fun updateWatchCount(@Path("id") videoId: String): Response<Unit>

    /**
     * @deprecated 此方法不符合RESTful原则，因为缓存状态是用户数据而非视频数据
     * 请使用 UserDataApi.updateUserVideoCache() 替代
     */
    @Deprecated(
        message = "使用 UserDataApi.updateUserVideoCache() 替代",
        replaceWith = ReplaceWith("userDataApi.updateUserVideoCache(userId, videoId, cacheRequest)")
    )
    @PUT("videos/{id}/cache-status")
    suspend fun updateVideoCacheStatus(@Path("id") videoId: String, @Body isCached: Boolean): Response<Unit>
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库管理器
替换原有的JSON文件存储
"""

import mysql.connector
from mysql.connector import pooling
import os
import json
from typing import List, Dict, Any, Optional

class MySQLManager:
    def __init__(self):
        self.config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'shuimu_server'),
            'password': os.getenv('DB_PASSWORD', 'dyj217'),
            'database': os.getenv('DB_NAME', 'shuimu_course_server'),
            'charset': 'utf8mb4',
            'autocommit': False  # 修复：关闭自动提交，使用显式事务管理
        }
        
        # 创建连接池
        self.pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name='shuimu_pool',
            pool_size=5,
            pool_reset_session=True,
            **self.config
        )
    
    def get_connection(self):
        """获取数据库连接"""
        return self.pool.get_connection()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params or ())
            result = cursor.fetchall()
            cursor.close()
            return result
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount
            conn.commit()  # 显式提交事务
            return affected_rows
        except Exception as e:
            if conn:
                conn.rollback()  # 发生错误时回滚
            print(f"❌ 数据库更新失败: {e}")
            print(f"SQL: {query}")
            print(f"参数: {params}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    # 系列相关方法
    def get_all_series(self) -> List[Dict]:
        """获取所有系列"""
        series_data = self.execute_query("""
            SELECT s.*, 
                   COUNT(DISTINCT c.id) as category_count,
                   COUNT(DISTINCT v.id) as video_count
            FROM series s
            LEFT JOIN categories c ON s.id = c.series_id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE s.is_published = 1
            GROUP BY s.id
            ORDER BY s.created_at DESC
        """)
        
        # 为每个系列添加分类ID列表
        for series in series_data:
            categories = self.execute_query("""
                SELECT id FROM categories WHERE series_id = %s ORDER BY order_index
            """, (series['id'],))
            series['categoryIds'] = [cat['id'] for cat in categories]
            
            # 转换数据类型以匹配原有API
            series['price'] = float(series['price']) if series['price'] else 0.0
            series['isFree'] = series['price'] == 0
            series['isPackage'] = False  # 默认值
            series['defaultExpanded'] = False  # 默认值
        
        return series_data
    
    def get_series_by_id(self, series_id: str) -> Optional[Dict]:
        """根据ID获取系列"""
        result = self.execute_query("""
            SELECT * FROM series WHERE id = %s
        """, (series_id,))
        
        if result:
            series = result[0]
            # 添加分类ID列表
            categories = self.execute_query("""
                SELECT id FROM categories WHERE series_id = %s ORDER BY order_index
            """, (series_id,))
            series['categoryIds'] = [cat['id'] for cat in categories]
            return series
        
        return None
    
    def update_series(self, series_id: str, data: Dict) -> bool:
        """更新系列"""
        affected_rows = self.execute_update("""
            UPDATE series 
            SET title = %s, description = %s, price = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('price', 0),
            series_id
        ))
        
        return affected_rows > 0
    
    # 分类相关方法
    def get_all_categories(self) -> List[Dict]:
        """获取所有分类"""
        categories_data = self.execute_query("""
            SELECT c.*,
                   s.title as series_title,
                   COUNT(v.id) as video_count
            FROM categories c
            LEFT JOIN series s ON c.series_id = s.id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE s.is_published = 1
            GROUP BY c.id
            ORDER BY c.series_id, c.order_index
        """)
        
        # 为每个分类添加视频列表
        for category in categories_data:
            videos = self.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category['id'],))
            
            # 转换视频数据格式
            category['videos'] = []
            for video in videos:
                video_dict = {
                    'id': video['id'],
                    'title': video['title'],
                    'description': video['description'],
                    'duration': video['duration'],
                    'cloudUrl': video['video_url'],
                    'localPath': '',  # 暂时为空
                    'playCount': 0,   # 暂时为0
                    'categoryId': video['category_id']
                }
                category['videos'].append(video_dict)
            
            # 转换分类数据格式
            category['seriesId'] = category['series_id']
            category['price'] = float(category['price']) if category['price'] else 0.0
            category['isFree'] = category['price'] == 0
            category['isPurchased'] = False  # 默认值
            category['defaultExpanded'] = False  # 默认值
            category['totalVideos'] = category['video_count']
            category['progress'] = 0.0  # 默认值
            category['watchCount'] = 0  # 默认值
        
        return categories_data
    
    def get_category_by_id(self, category_id: str) -> Optional[Dict]:
        """根据ID获取分类"""
        result = self.execute_query("""
            SELECT * FROM categories WHERE id = %s
        """, (category_id,))
        
        if result:
            category = result[0]
            # 添加视频列表
            videos = self.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category_id,))
            category['videos'] = videos
            return category
        
        return None
    
    def update_category(self, category_id: str, data: Dict) -> bool:
        """更新分类"""
        affected_rows = self.execute_update("""
            UPDATE categories 
            SET title = %s, description = %s, price = %s, order_index = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('price', 0),
            data.get('order_index', 0),
            category_id
        ))
        
        return affected_rows > 0
    
    # 视频相关方法
    def get_all_videos(self) -> List[Dict]:
        """获取所有视频"""
        videos_data = self.execute_query("""
            SELECT v.*, 
                   c.title as category_title,
                   c.series_id
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            ORDER BY c.series_id, c.order_index, v.order_index
        """)
        
        # 转换数据格式
        for video in videos_data:
            video['categoryId'] = video['category_id']
            video['cloudUrl'] = video['video_url']
            video['localPath'] = ''  # 暂时为空
            video['playCount'] = 0   # 暂时为0
        
        return videos_data
    
    def get_video_by_id(self, video_id: str) -> Optional[Dict]:
        """根据ID获取视频"""
        result = self.execute_query("""
            SELECT * FROM videos WHERE id = %s
        """, (video_id,))
        
        return result[0] if result else None
    
    def update_video(self, video_id: str, data: Dict) -> bool:
        """更新视频"""
        affected_rows = self.execute_update("""
            UPDATE videos 
            SET title = %s, description = %s, video_url = %s, duration = %s, order_index = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('video_url'),
            data.get('duration', 0),
            data.get('order_index', 0),
            video_id
        ))
        
        return affected_rows > 0

# 全局实例
mysql_manager = MySQLManager()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Dict, List, Any, Optional
import threading
from datetime import datetime

class UserManagementFrame(ttk.Frame):
    """用户管理界面"""
    
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.users_data = []
        self.current_page = 1
        self.page_size = 20
        self.total_pages = 1
        
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ttk.Label(self, text="用户管理", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 工具栏
        toolbar_frame = ttk.Frame(self)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 搜索框
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.search_entry.bind('<Return>', lambda e: self.search_users())
        
        ttk.Button(search_frame, text="搜索", command=self.search_users).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(search_frame, text="重置", command=self.reset_search).pack(side=tk.LEFT)
        
        # 操作按钮
        button_frame = ttk.Frame(toolbar_frame)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="创建用户", command=self.create_user).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新", command=self.refresh_users).pack(side=tk.LEFT, padx=5)
        
        # 筛选选项
        filter_frame = ttk.Frame(self)
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(filter_frame, text="状态筛选:").pack(side=tk.LEFT, padx=(0, 5))
        self.status_var = tk.StringVar(value="all")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_var, 
                                   values=["all", "active", "inactive"], state="readonly", width=10)
        status_combo.pack(side=tk.LEFT, padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_users())
        
        ttk.Label(filter_frame, text="角色筛选:").pack(side=tk.LEFT, padx=(10, 5))
        self.role_var = tk.StringVar(value="all")
        role_combo = ttk.Combobox(filter_frame, textvariable=self.role_var,
                                 values=["all", "admin", "user"], state="readonly", width=10)
        role_combo.pack(side=tk.LEFT, padx=(0, 10))
        role_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_users())
        
        # 用户列表
        self.setup_user_list()
        
        # 分页控件
        self.setup_pagination()
        
        # 状态栏
        self.status_bar = ttk.Label(self, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_user_list(self):
        """设置用户列表"""
        # 创建Treeview
        columns = ("ID", "用户名", "邮箱", "显示名", "状态", "角色", "最后登录", "创建时间")
        self.tree = ttk.Treeview(self, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {
            "ID": 100,
            "用户名": 120,
            "邮箱": 200,
            "显示名": 120,
            "状态": 80,
            "角色": 80,
            "最后登录": 150,
            "创建时间": 150
        }
        
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_column(c))
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 滚动条
        scrollbar_y = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        tree_frame = ttk.Frame(self)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_user_double_click)
        
        # 右键菜单
        self.setup_context_menu()
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self.view_user_details)
        self.context_menu.add_command(label="编辑用户", command=self.edit_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="启用用户", command=self.enable_user)
        self.context_menu.add_command(label="禁用用户", command=self.disable_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除用户", command=self.delete_user)
        
        self.tree.bind("<Button-3>", self.show_context_menu)
    
    def setup_pagination(self):
        """设置分页控件"""
        pagination_frame = ttk.Frame(self)
        pagination_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 页面信息
        self.page_info_var = tk.StringVar(value="第 1 页，共 1 页")
        ttk.Label(pagination_frame, textvariable=self.page_info_var).pack(side=tk.LEFT)
        
        # 分页按钮
        button_frame = ttk.Frame(pagination_frame)
        button_frame.pack(side=tk.RIGHT)
        
        self.prev_button = ttk.Button(button_frame, text="上一页", command=self.prev_page)
        self.prev_button.pack(side=tk.LEFT, padx=2)
        
        self.next_button = ttk.Button(button_frame, text="下一页", command=self.next_page)
        self.next_button.pack(side=tk.LEFT, padx=2)
        
        # 跳转页面
        ttk.Label(button_frame, text="跳转到:").pack(side=tk.LEFT, padx=(10, 2))
        self.page_entry = ttk.Entry(button_frame, width=5)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        self.page_entry.bind('<Return>', lambda e: self.goto_page())
        
        ttk.Button(button_frame, text="跳转", command=self.goto_page).pack(side=tk.LEFT, padx=2)
    
    def load_users(self, page: int = 1, search: str = None):
        """加载用户数据"""
        def load_in_thread():
            try:
                self.update_status("正在加载用户数据...")
                
                # 构建查询参数
                params = {
                    'page': page,
                    'page_size': self.page_size
                }
                
                if search:
                    params['search'] = search
                
                # 状态筛选
                if self.status_var.get() == "active":
                    params['is_active'] = True
                elif self.status_var.get() == "inactive":
                    params['is_active'] = False
                
                # 角色筛选
                if self.role_var.get() == "admin":
                    params['is_admin'] = True
                elif self.role_var.get() == "user":
                    params['is_admin'] = False
                
                # 调用API
                response = self.api_client.get_users(**params)
                
                if response.get('success'):
                    self.users_data = response.get('data', [])
                    pagination = response.get('pagination', {})
                    self.current_page = pagination.get('page', 1)
                    self.total_pages = pagination.get('pages', 1)
                    
                    # 更新UI
                    self.after(0, self.update_user_list)
                    self.after(0, self.update_pagination_info)
                    self.after(0, lambda: self.update_status(f"加载完成，共 {len(self.users_data)} 个用户"))
                else:
                    error_msg = response.get('message', '加载用户失败')
                    self.after(0, lambda: self.update_status(f"错误: {error_msg}"))
                    self.after(0, lambda: messagebox.showerror("错误", error_msg))
                    
            except Exception as e:
                error_msg = f"加载用户失败: {str(e)}"
                self.after(0, lambda: self.update_status(error_msg))
                self.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def update_user_list(self):
        """更新用户列表显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新数据
        for user in self.users_data:
            values = (
                user.get('id', '')[:8] + '...' if len(user.get('id', '')) > 8 else user.get('id', ''),
                user.get('username', ''),
                user.get('email', ''),
                user.get('display_name', ''),
                "活跃" if user.get('is_active') else "禁用",
                "管理员" if user.get('is_admin') else "用户",
                self.format_datetime(user.get('last_login_at')),
                self.format_datetime(user.get('created_at'))
            )
            
            # 根据状态设置不同的标签
            tags = []
            if not user.get('is_active'):
                tags.append('inactive')
            if user.get('is_admin'):
                tags.append('admin')
            
            self.tree.insert("", tk.END, values=values, tags=tags)
        
        # 设置标签样式
        self.tree.tag_configure('inactive', foreground='gray')
        self.tree.tag_configure('admin', foreground='blue')
    
    def format_datetime(self, dt_str: str) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "从未"
        try:
            # 尝试解析ISO格式
            if 'T' in dt_str:
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M")
            return dt_str
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        info_text = f"第 {self.current_page} 页，共 {self.total_pages} 页"
        self.page_info_var.set(info_text)
        
        # 更新按钮状态
        self.prev_button.config(state=tk.NORMAL if self.current_page > 1 else tk.DISABLED)
        self.next_button.config(state=tk.NORMAL if self.current_page < self.total_pages else tk.DISABLED)
    
    def update_status(self, message: str):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    # 事件处理方法
    def search_users(self):
        """搜索用户"""
        search_text = self.search_var.get().strip()
        self.current_page = 1
        self.load_users(page=1, search=search_text if search_text else None)
    
    def reset_search(self):
        """重置搜索"""
        self.search_var.set("")
        self.status_var.set("all")
        self.role_var.set("all")
        self.current_page = 1
        self.load_users(page=1)
    
    def filter_users(self):
        """筛选用户"""
        self.current_page = 1
        search_text = self.search_var.get().strip()
        self.load_users(page=1, search=search_text if search_text else None)
    
    def refresh_users(self):
        """刷新用户列表"""
        search_text = self.search_var.get().strip()
        self.load_users(page=self.current_page, search=search_text if search_text else None)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            search_text = self.search_var.get().strip()
            self.load_users(page=self.current_page - 1, search=search_text if search_text else None)
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            search_text = self.search_var.get().strip()
            self.load_users(page=self.current_page + 1, search=search_text if search_text else None)
    
    def goto_page(self):
        """跳转到指定页面"""
        try:
            page = int(self.page_entry.get())
            if 1 <= page <= self.total_pages:
                search_text = self.search_var.get().strip()
                self.load_users(page=page, search=search_text if search_text else None)
                self.page_entry.delete(0, tk.END)
            else:
                messagebox.showwarning("警告", f"页面号必须在 1 到 {self.total_pages} 之间")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的页面号")
    
    def sort_column(self, col):
        """排序列"""
        # TODO: 实现列排序功能
        pass
    
    def get_selected_user(self) -> Optional[Dict]:
        """获取选中的用户"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        values = self.tree.item(item, 'values')
        if not values:
            return None
        
        # 根据用户名查找完整的用户数据
        username = values[1]
        for user in self.users_data:
            if user.get('username') == username:
                return user
        return None
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def on_user_double_click(self, event):
        """双击用户事件"""
        self.view_user_details()
    
    def view_user_details(self):
        """查看用户详情"""
        user = self.get_selected_user()
        if not user:
            messagebox.showwarning("警告", "请选择一个用户")
            return
        
        # 创建用户详情窗口
        from .user_detail_dialog import UserDetailDialog
        dialog = UserDetailDialog(self, user, self.api_client)
        dialog.show()
    
    def edit_user(self):
        """编辑用户"""
        user = self.get_selected_user()
        if not user:
            messagebox.showwarning("警告", "请选择一个用户")
            return
        
        # 创建用户编辑窗口
        from .user_edit_dialog import UserEditDialog
        dialog = UserEditDialog(self, user, self.api_client)
        if dialog.show():
            self.refresh_users()
    
    def create_user(self):
        """创建用户"""
        from .user_create_dialog import UserCreateDialog
        dialog = UserCreateDialog(self, self.api_client)
        if dialog.show():
            self.refresh_users()
    
    def enable_user(self):
        """启用用户"""
        user = self.get_selected_user()
        if not user:
            messagebox.showwarning("警告", "请选择一个用户")
            return
        
        if user.get('is_active'):
            messagebox.showinfo("信息", "用户已经是活跃状态")
            return
        
        if messagebox.askyesno("确认", f"确定要启用用户 '{user.get('username')}' 吗？"):
            self.update_user_status(user.get('id'), True)
    
    def disable_user(self):
        """禁用用户"""
        user = self.get_selected_user()
        if not user:
            messagebox.showwarning("警告", "请选择一个用户")
            return
        
        if not user.get('is_active'):
            messagebox.showinfo("信息", "用户已经是禁用状态")
            return
        
        if messagebox.askyesno("确认", f"确定要禁用用户 '{user.get('username')}' 吗？"):
            self.update_user_status(user.get('id'), False)
    
    def delete_user(self):
        """删除用户"""
        user = self.get_selected_user()
        if not user:
            messagebox.showwarning("警告", "请选择一个用户")
            return
        
        username = user.get('username')
        if messagebox.askyesno("确认删除", 
                              f"确定要删除用户 '{username}' 吗？\n\n"
                              f"此操作不可撤销，用户的所有数据将被删除。"):
            self.perform_delete_user(user.get('id'))
    
    def update_user_status(self, user_id: str, is_active: bool):
        """更新用户状态"""
        def update_in_thread():
            try:
                self.update_status("正在更新用户状态...")
                
                response = self.api_client.update_user(user_id, {'is_active': is_active})
                
                if response.get('success'):
                    action = "启用" if is_active else "禁用"
                    self.after(0, lambda: self.update_status(f"用户{action}成功"))
                    self.after(0, self.refresh_users)
                else:
                    error_msg = response.get('message', '更新用户状态失败')
                    self.after(0, lambda: self.update_status(f"错误: {error_msg}"))
                    self.after(0, lambda: messagebox.showerror("错误", error_msg))
                    
            except Exception as e:
                error_msg = f"更新用户状态失败: {str(e)}"
                self.after(0, lambda: self.update_status(error_msg))
                self.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        threading.Thread(target=update_in_thread, daemon=True).start()
    
    def perform_delete_user(self, user_id: str):
        """执行删除用户"""
        def delete_in_thread():
            try:
                self.update_status("正在删除用户...")
                
                response = self.api_client.delete_user(user_id)
                
                if response.get('success'):
                    self.after(0, lambda: self.update_status("用户删除成功"))
                    self.after(0, self.refresh_users)
                    self.after(0, lambda: messagebox.showinfo("成功", "用户删除成功"))
                else:
                    error_msg = response.get('message', '删除用户失败')
                    self.after(0, lambda: self.update_status(f"错误: {error_msg}"))
                    self.after(0, lambda: messagebox.showerror("错误", error_msg))
                    
            except Exception as e:
                error_msg = f"删除用户失败: {str(e)}"
                self.after(0, lambda: self.update_status(error_msg))
                self.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        threading.Thread(target=delete_in_thread, daemon=True).start()

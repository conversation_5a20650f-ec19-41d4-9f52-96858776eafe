# RESTful API 改进执行方案

## 📋 方案概述

基于对当前项目的RESTful符合性分析，制定以下分阶段改进方案。

## 🎯 改进目标

1. **App端**：迁移到正确的用户数据端点
2. **服务端**：废弃旧端点，完善RESTful架构
3. **管理端**：保持当前良好状态，无需改进

## 📊 当前问题总结

### ❌ App端主要问题
```kotlin
// 错误的端点使用（将用户数据当作视频数据）
PUT /api/videos/{id}/progress        // ❌ 应该是用户进度数据
PUT /api/videos/{id}/cache-status    // ❌ 应该是用户缓存数据  
PUT /api/videos/{id}/watch-count     // ❌ 应该是用户统计数据
```

### ⚠️ 服务端需要改进
```python
# 旧端点仍然存在，需要废弃处理
PUT /api/videos/{id}/progress        # 需要标记为废弃
PUT /api/videos/{id}/cache-status    # 需要标记为废弃
```

### ✅ 管理端状态良好
- 已正确使用RESTful端点
- 权限控制完善
- API调用规范

---

## 🚀 阶段一：App端用户数据API迁移（高优先级）

### 📱 步骤1：创建新的UserDataApi接口

**文件**：`app/src/main/java/com/shuimu/course/data/remote/api/UserDataApi.kt`

```kotlin
interface UserDataApi {
    @PUT("users/{userId}/progress/{videoId}")
    suspend fun updateUserProgress(
        @Path("userId") userId: String,
        @Path("videoId") videoId: String,
        @Body progress: UserProgressRequest
    ): Response<Unit>
    
    @PUT("users/{userId}/cache/{videoId}")
    suspend fun updateUserCache(
        @Path("userId") userId: String,
        @Path("videoId") videoId: String,
        @Body cacheData: UserCacheRequest
    ): Response<Unit>
    
    @PUT("users/{userId}/stats")
    suspend fun updateUserStats(
        @Path("userId") userId: String,
        @Body stats: UserStatsRequest
    ): Response<Unit>
    
    @GET("users/{userId}/profile")
    suspend fun getUserProfile(
        @Path("userId") userId: String
    ): Response<UserProfileDto>
}
```

### 📱 步骤2：创建数据模型

**文件**：`app/src/main/java/com/shuimu/course/data/remote/dto/UserDataDto.kt`

```kotlin
data class UserProgressRequest(
    val position: Int? = null,
    val progress: Float? = null,
    val watchCount: Int? = null,
    val isCompleted: Boolean? = null,
    val lastWatchedAt: String? = null
)

data class UserCacheRequest(
    val isCached: Boolean,
    val localPath: String? = null,
    val fileSize: Long? = null,
    val cachedAt: String? = null
)

data class UserStatsRequest(
    val totalWatchTime: Long? = null,
    val dailyWatchTime: Long? = null,
    val completedVideos: Int? = null
)
```

### 📱 步骤3：更新Repository层

**文件**：`app/src/main/java/com/shuimu/course/data/repository/VideoRepositoryImpl.kt`

```kotlin
// 替换现有的进度更新方法
suspend fun updateVideoProgress(videoId: String, progress: Int) {
    val userId = getCurrentUserId() // 获取当前用户ID
    val progressRequest = UserProgressRequest(
        position = progress,
        progress = progress / 100.0f,
        lastWatchedAt = System.currentTimeMillis().toString()
    )
    
    try {
        // 使用新的用户数据端点
        userDataApi.updateUserProgress(userId, videoId, progressRequest)
    } catch (e: Exception) {
        // 降级到本地存储
        saveProgressLocally(videoId, progress)
    }
}
```

### 📱 步骤4：依赖注入配置

**文件**：`app/src/main/java/com/shuimu/course/di/NetworkModule.kt`

```kotlin
@Provides
@Singleton
fun provideUserDataApi(retrofit: Retrofit): UserDataApi {
    return retrofit.create(UserDataApi::class.java)
}
```

---

## 🖥️ 阶段二：服务端旧端点废弃处理（高优先级）

### 🔧 步骤1：标记旧端点为废弃

**文件**：`mock_server/src/api/videos.py`

```python
@router.put("/videos/{video_id}/progress", deprecated=True)
def update_video_progress_deprecated(video_id: str):
    """
    废弃端点：更新视频进度
    
    ⚠️ 此端点已废弃，请使用用户数据端点：
    PUT /api/users/{user_id}/progress/{video_id}
    
    废弃时间：2025-06-30
    移除时间：2025-09-30
    """
    return {
        "success": False,
        "message": "端点已废弃",
        "deprecated": True,
        "replacement": "PUT /api/users/{user_id}/progress/{video_id}",
        "deprecation_date": "2025-06-30",
        "removal_date": "2025-09-30"
    }

@router.put("/videos/{video_id}/cache-status", deprecated=True)
def update_video_cache_deprecated(video_id: str):
    """废弃端点：更新视频缓存状态"""
    return {
        "success": False,
        "message": "端点已废弃",
        "deprecated": True,
        "replacement": "PUT /api/users/{user_id}/cache/{video_id}",
        "deprecation_date": "2025-06-30",
        "removal_date": "2025-09-30"
    }
```

### 🔧 步骤2：添加迁移提示中间件

**文件**：`mock_server/src/middleware/deprecation.py`

```python
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware

class DeprecationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # 检查是否是废弃端点
        deprecated_endpoints = [
            "/api/videos/{id}/progress",
            "/api/videos/{id}/cache-status",
            "/api/videos/{id}/watch-count"
        ]
        
        for endpoint in deprecated_endpoints:
            if endpoint.replace("{id}", "") in str(request.url):
                response.headers["X-Deprecated"] = "true"
                response.headers["X-Replacement"] = "See API documentation"
                response.headers["X-Removal-Date"] = "2025-09-30"
                break
        
        return response
```

### 🔧 步骤3：更新API文档

**文件**：`mock_server/src/main.py`

```python
app = FastAPI(
    title="Shuimu Video Course API",
    description="""
    水幕视频课程API
    
    ⚠️ 重要通知：
    以下端点已废弃，将于2025-09-30移除：
    - PUT /api/videos/{id}/progress → PUT /api/users/{user_id}/progress/{video_id}
    - PUT /api/videos/{id}/cache-status → PUT /api/users/{user_id}/cache/{video_id}
    
    请尽快迁移到新的用户数据端点。
    """,
    version="2.0.0"
)
```

---

## 📅 执行时间表

### 🗓️ 第1周（2025-06-30 - 2025-07-06）
- [ ] App端创建UserDataApi接口
- [ ] 服务端标记旧端点为废弃
- [ ] 更新API文档

### 🗓️ 第2-3周（2025-07-07 - 2025-07-20）
- [ ] App端Repository层迁移
- [ ] 全面测试新端点
- [ ] 修复发现的问题

### 🗓️ 第4周（2025-07-21 - 2025-07-27）
- [ ] 生产环境部署
- [ ] 监控新端点使用情况
- [ ] 收集用户反馈

### 🗓️ 3个月后（2025-09-30）
- [ ] 移除废弃端点
- [ ] 清理相关代码
- [ ] 更新文档

---

## 🧪 测试计划

### 📱 App端测试
```kotlin
@Test
fun testUserProgressUpdate() {
    // 测试新的用户进度更新端点
    val userId = "test_user_001"
    val videoId = "test_video_001"
    val progress = UserProgressRequest(position = 50, progress = 0.5f)
    
    runBlocking {
        val response = userDataApi.updateUserProgress(userId, videoId, progress)
        assertTrue(response.isSuccessful)
    }
}
```

### 🖥️ 服务端测试
```python
def test_deprecated_endpoint():
    """测试废弃端点返回正确的废弃信息"""
    response = client.put("/api/videos/test_id/progress", json={"progress": 50})
    assert response.status_code == 200
    assert response.json()["deprecated"] == True
    assert "replacement" in response.json()
```

---

## 📊 成功指标

### 📈 技术指标
- [ ] App端100%迁移到新端点
- [ ] 旧端点使用率降至0%
- [ ] API响应时间保持稳定
- [ ] 错误率不超过1%

### 📋 业务指标
- [ ] 用户体验无明显影响
- [ ] 数据一致性保持100%
- [ ] 开发效率提升20%

---

## 🚨 风险控制

### ⚠️ 主要风险
1. **数据丢失风险**：迁移过程中可能丢失用户数据
2. **兼容性风险**：新旧端点数据格式不一致
3. **性能风险**：新端点可能影响性能

### 🛡️ 风险缓解
1. **数据备份**：迁移前完整备份所有用户数据
2. **灰度发布**：分批次迁移用户
3. **回滚机制**：保持旧端点3个月兼容期
4. **监控告警**：实时监控新端点性能和错误率

---

**文档版本**：v1.0  
**创建时间**：2025-06-30  
**负责人**：开发团队

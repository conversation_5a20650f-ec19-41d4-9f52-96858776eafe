#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系列API - 使用MySQL数据库
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime
from ..database.mysql_manager import mysql_manager

router = APIRouter()

# 数据模型
class SeriesCreateRequest(BaseModel):
    title: str
    description: Optional[str] = ""
    price: Optional[float] = 0.0
    is_published: Optional[bool] = True

class SeriesUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    is_published: Optional[bool] = None

@router.get("/series")
def get_all_series(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_published: Optional[bool] = Query(None)
):
    """获取系列列表"""
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if search:
            where_conditions.append("(title LIKE %s OR description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if is_published is not None:
            where_conditions.append("is_published = %s")
            params.append(is_published)
        else:
            # 默认只返回已发布的系列
            where_conditions.append("is_published = 1")

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM series WHERE {where_clause}"
        total_result = mysql_manager.execute_query(count_query, tuple(params))
        total = total_result[0]['total'] if total_result else 0

        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f"""
            SELECT s.*,
                   COUNT(DISTINCT c.id) as category_count,
                   COUNT(DISTINCT v.id) as video_count
            FROM series s
            LEFT JOIN categories c ON s.id = c.series_id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE {where_clause}
            GROUP BY s.id
            ORDER BY s.created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        series_data = mysql_manager.execute_query(data_query, tuple(params))

        # 为每个系列添加分类ID列表
        for series in series_data:
            categories = mysql_manager.execute_query("""
                SELECT id FROM categories WHERE series_id = %s ORDER BY order_index
            """, (series['id'],))
            series['categoryIds'] = [cat['id'] for cat in categories]

            # 转换数据类型以匹配原有API
            series['price'] = float(series['price']) if series['price'] else 0.0
            series['isFree'] = series['price'] == 0
            series['isPackage'] = False
            series['defaultExpanded'] = False

        return {
            'success': True,
            'data': series_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'pages': (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系列列表失败: {str(e)}")

@router.get("/series/{series_id}")
def get_series_by_id(series_id: str):
    """根据ID获取系列"""
    try:
        series = mysql_manager.get_series_by_id(series_id)
        if not series:
            raise HTTPException(status_code=404, detail="系列不存在")
        return series
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系列失败: {str(e)}")

@router.post("/admin/series")
def create_series(data: SeriesCreateRequest):
    """创建系列（管理端）"""
    try:
        # 生成新ID
        new_id = str(uuid.uuid4())

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO series (id, title, description, price, is_published, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            data.title,
            data.description,
            data.price,
            data.is_published
        ))

        if affected_rows > 0:
            # 验证数据是否真的保存到数据库
            created_series = mysql_manager.get_series_by_id(new_id)

            if not created_series:
                print(f"❌ 系列创建验证失败: ID {new_id} 在数据库中不存在")
                raise HTTPException(status_code=500, detail="系列创建失败：数据未正确保存到数据库")

            print(f"✅ 系列创建成功并验证: {created_series['title']} (ID: {new_id})")

            return {
                "success": True,
                "message": "系列创建成功",
                "data": {
                    "id": new_id,
                    "title": data.title,
                    "description": data.description,
                    "price": data.price,
                    "is_published": data.is_published,
                    "isFree": data.price == 0,
                    "isPackage": False,
                    "defaultExpanded": False,
                    "categoryIds": [],
                    "created_at": created_series.get('created_at', datetime.now().isoformat()),
                    "updated_at": created_series.get('updated_at', datetime.now().isoformat())
                }
            }
        else:
            print(f"❌ 系列创建失败: affected_rows = {affected_rows}")
            raise HTTPException(status_code=500, detail="创建系列失败：数据库操作未影响任何行")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建系列失败: {str(e)}")

@router.put("/admin/series/{series_id}")
def update_series(series_id: str, data: SeriesUpdateRequest):
    """更新系列（管理端）"""
    try:
        # 检查系列是否存在
        existing_series = mysql_manager.get_series_by_id(series_id)
        if not existing_series:
            raise HTTPException(status_code=404, detail="系列不存在")

        # 更新系列
        update_data = data.dict(exclude_unset=True)
        success = mysql_manager.update_series(series_id, update_data)

        if success:
            return {
                "success": True,
                "message": "系列更新成功",
                "data": mysql_manager.get_series_by_id(series_id)
            }
        else:
            raise HTTPException(status_code=500, detail="更新系列失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新系列失败: {str(e)}")

@router.delete("/admin/series/{series_id}")
def delete_series(series_id: str):
    """删除系列（管理端）"""
    try:
        # 检查系列是否存在
        existing_series = mysql_manager.get_series_by_id(series_id)
        if not existing_series:
            raise HTTPException(status_code=404, detail="系列不存在")

        # 检查是否有关联的分类
        categories = mysql_manager.execute_query("""
            SELECT COUNT(*) as count FROM categories WHERE series_id = %s
        """, (series_id,))

        if categories[0]['count'] > 0:
            return {
                "success": False,
                "message": f"无法删除系列，还有{categories[0]['count']}个关联分类"
            }

        # 删除系列
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM series WHERE id = %s
        """, (series_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "系列删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除系列失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除系列失败: {str(e)}")

# 层级关系查询端点
@router.get("/series/{series_id}/categories")
def get_categories_by_series(series_id: str):
    """获取系列下的所有分类"""
    try:
        # 验证系列是否存在
        series = mysql_manager.get_series_by_id(series_id)
        if not series:
            raise HTTPException(status_code=404, detail="系列不存在")

        # 获取系列下的分类
        categories = mysql_manager.execute_query("""
            SELECT c.*, COUNT(v.id) as video_count
            FROM categories c
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE c.series_id = %s
            GROUP BY c.id
            ORDER BY c.order_index
        """, (series_id,))

        # 为每个分类添加视频列表
        for category in categories:
            videos = mysql_manager.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category['id'],))

            # 转换视频数据格式
            category['videos'] = []
            for video in videos:
                video_dict = {
                    'id': video['id'],
                    'title': video['title'],
                    'description': video['description'],
                    'duration': video['duration'],
                    'cloudUrl': video['video_url'],
                    'localPath': '',
                    'playCount': 0,
                    'categoryId': video['category_id']
                }
                category['videos'].append(video_dict)

            # 转换分类数据格式
            category['seriesId'] = category['series_id']
            category['price'] = float(category['price']) if category['price'] else 0.0
            category['isFree'] = category['price'] == 0
            category['isPurchased'] = False
            category['defaultExpanded'] = False
            category['totalVideos'] = category['video_count']
            category['progress'] = 0.0
            category['watchCount'] = 0

        return {
            'success': True,
            'data': categories
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系列分类失败: {str(e)}")
